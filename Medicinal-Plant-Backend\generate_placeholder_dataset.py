"""Generate a tiny placeholder dataset for testing training and serving.
Creates data/sample with two classes, writes labels.csv and models/classes.json,
and writes one placeholder image per class in train and val.
"""
from pathlib import Path
from PIL import Image
import json

root = Path(__file__).parent
data_dir = root / 'data' / 'sample'
train = data_dir / 'train'
val = data_dir / 'val'
classes = [
    ('aloe_vera', 'Aloe vera', 'Aloe', 'Anti-inflammatory|Wound healing'),
    ('neem', 'Azadirachta indica', 'Neem', 'Anti-bacterial|Anti-fungal')
]

def ensure(p):
    p.mkdir(parents=True, exist_ok=True)

def make_img(p, text):
    img = Image.new('RGB', (224,224), color=(180,220,200))
    img.save(p)

def main():
    # clear existing
    if data_dir.exists():
        import shutil
        shutil.rmtree(data_dir)
    for cls, sci, local, feat in classes:
        ensure(train / cls)
        ensure(val / cls)
        make_img(train / cls / f'{cls}_0.jpg', cls)
        make_img(val / cls / f'{cls}_0.jpg', cls)

    # labels.csv
    with open(root / 'labels.csv', 'w', encoding='utf-8') as fh:
        fh.write('class_name,scientificName,localName,medicinalFeature\n')
        for cls, sci, local, feat in classes:
            fh.write(f'{cls},{sci},{local},{feat}\n')

    # models/classes.json
    ensure(root / 'models')
    mapping = {str(i): cls for i, (cls, *_ ) in enumerate(classes)}
    with open(root / 'models' / 'classes.json', 'w', encoding='utf-8') as fh:
        json.dump(mapping, fh, indent=2)

    print('Placeholder dataset and labels.json created')

if __name__ == '__main__':
    main()
