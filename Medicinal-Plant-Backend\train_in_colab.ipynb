{"cells": [{"cell_type": "markdown", "id": "0ba37d3e", "metadata": {}, "source": ["# Train Medicinal Plant Model (Colab + Diagnostic)\n", "\n", "This notebook helps in two ways:\n", "\n", "1. Run a transfer-learning training job (MobileNetV2) on the `data/oneplant` or `data/sample` dataset and save `plant_model.h5` + `classes.json` to Google Drive.\n", "2. Diagnose and fix local environment issues on Windows (venv/conda, requirements, OpenCV/TensorFlow compatibility) so you can also run training locally if you prefer.\n", "\n", "Instructions:\n", "- If using Google Colab: upload your `Medicinal-Plant-Backend` folder as a zip or copy it to your Drive, then run the cells in order.\n", "- If running locally in a Jupyter environment: this notebook includes diagnostic cells to fix environment problems. Run those cells to create a venv and install packages.\n", "\n", "Run cells in order. If you plan to use Colab and Train there, mount your Drive in the next cell."]}, {"cell_type": "code", "execution_count": null, "id": "378ce29f", "metadata": {}, "outputs": [], "source": ["# Cell 2: Mount Google Drive (optional for Colab)\n", "from pathlib import Path\n", "try:\n", "    from google.colab import drive\n", "    IN_COLAB = True\n", "except Exception:\n", "    IN_COLAB = False\n", "\n", "if IN_COLAB:\n", "    print('Running in Colab: mounting Drive...')\n", "    drive.mount('/content/drive')\n", "else:\n", "    print('Not in Colab. Set IN_COLAB=True to mount Drive in Colab.')\n", "\n", "# helper paths\n", "PROJECT_ROOT = Path('/content/Medicinal-Plant-Backend')\n", "DRIVE_BASE = Path('/content/drive/MyDrive/Medicinal')\n", "PROJECT_ROOT.mkdir(parents=True, exist_ok=True)\n", "DRIVE_BASE.mkdir(parents=True, exist_ok=True)\n", "print('project_root:', PROJECT_ROOT)\n", "print('drive_base:', DRIVE_BASE)\n"]}, {"cell_type": "code", "execution_count": null, "id": "9423ecae", "metadata": {}, "outputs": [], "source": ["# Cell 3: Optionally upload a zip of the repository in Colab\n", "if IN_COLAB:\n", "    from google.colab import files\n", "    print('You may upload a zip of your backend repo now (press Enter to skip)')\n", "    # files.upload()  # uncomment if you want interactive upload\n", "    # If uploaded, user should extract and place under /content/Medicinal-Plant-Backend\n", "else:\n", "    print('Not in Colab; skip upload cell')\n"]}, {"cell_type": "code", "execution_count": null, "id": "d6f2c52e", "metadata": {}, "outputs": [], "source": ["# Cell 4: Install required packages (Colab or local)\n", "# In Colab this will install TensorFlow and other deps; locally this will attempt to install into current Python env.\n", "import sys\n", "print('python', sys.version)\n", "\n", "if IN_COLAB:\n", "    # install a compatible TF version in Colab\n", "    !pip install -q tensorflow==2.12.0 tinydb pillow\n", "else:\n", "    print('Not running in Colab. To install locally, create/activate a venv or conda env and run:')\n", "    print('python -m pip install --upgrade pip')\n", "    print('python -m pip install -r requirements.txt')\n"]}, {"cell_type": "code", "execution_count": null, "id": "eef7c63c", "metadata": {}, "outputs": [], "source": ["# Cell 5: Locate dataset - try common locations\n", "from pathlib import Path\n", "candidates = [Path('/content/Medicinal-Plant-Backend/data/oneplant'), Path('/content/Medicinal-Plant-Backend/data/sample'), DRIVE_BASE / 'data' / 'oneplant']\n", "DATA_DIR = None\n", "for p in candidates:\n", "    if p.exists():\n", "        DATA_DIR = p\n", "        break\n", "print('Selected dataset directory:', DATA_DIR)\n", "\n", "if DATA_DIR is None:\n", "    print('No dataset found in default locations. If in Colab, upload or copy your dataset to /content/Medicinal-Plant-Backend/data/oneplant or mount Drive and place it under /content/drive/MyDrive/Medicinal/data/oneplant')\n"]}, {"cell_type": "code", "execution_count": null, "id": "1309e729", "metadata": {}, "outputs": [], "source": ["# Cell 6: Build tf.data datasets (if dataset found)\n", "if DATA_DIR is not None:\n", "    import tensorflow as tf\n", "    IMG_SIZE = 224\n", "    BATCH_SIZE = 16\n", "    train_ds = tf.keras.utils.image_dataset_from_directory(\n", "        str(DATA_DIR),\n", "        labels='inferred',\n", "        label_mode='int',\n", "        batch_size=BATCH_SIZE,\n", "        image_size=(IMG_SIZE, IMG_SIZE),\n", "        validation_split=0.2,\n", "        subset='training',\n", "        seed=123)\n", "    val_ds = tf.keras.utils.image_dataset_from_directory(\n", "        str(DATA_DIR),\n", "        labels='inferred',\n", "        label_mode='int',\n", "        batch_size=BATCH_SIZE,\n", "        image_size=(IMG_SIZE, IMG_SIZE),\n", "        validation_split=0.2,\n", "        subset='validation',\n", "        seed=123)\n", "    class_names = train_ds.class_names\n", "    print('Classes:', class_names)\n", "else:\n", "    print('Skipping dataset loading because DATA_DIR is None')\n"]}, {"cell_type": "code", "execution_count": null, "id": "c15f027b", "metadata": {}, "outputs": [], "source": ["# Cell 7: Build and compile MobileNetV2 transfer model\n", "if DATA_DIR is not None:\n", "    import tensorflow as tf\n", "    from tensorflow.keras import layers\n", "    base_model = tf.keras.applications.MobileNetV2(input_shape=(IMG_SIZE, IMG_SIZE, 3), include_top=False, weights='imagenet')\n", "    base_model.trainable = False\n", "    inputs = tf.keras.Input(shape=(IMG_SIZE, IMG_SIZE, 3))\n", "    x = tf.keras.applications.mobilenet_v2.preprocess_input(inputs)\n", "    x = base_model(x, training=False)\n", "    x = layers.GlobalAveragePooling2D()(x)\n", "    x = layers.Dropout(0.2)(x)\n", "    outputs = layers.Dense(len(class_names), activation='softmax')(x)\n", "    model = tf.keras.Model(inputs, outputs)\n", "    model.compile(optimizer='adam', loss='sparse_categorical_crossentropy', metrics=['accuracy'])\n", "    model.summary()\n", "else:\n", "    print('Skipping model build (no DATA_DIR)')\n"]}, {"cell_type": "code", "execution_count": null, "id": "4dfc9ec8", "metadata": {}, "outputs": [], "source": ["# Cell 8: Train the model (if built)\n", "if DATA_DIR is not None:\n", "    EPOCHS = 8\n", "    history = model.fit(train_ds, validation_data=val_ds, epochs=EPOCHS)\n", "    print('Training finished')\n", "else:\n", "    print('Skipping training')\n"]}, {"cell_type": "code", "execution_count": null, "id": "2191a50e", "metadata": {}, "outputs": [], "source": ["# Cell 9: Save model and classes mapping to Drive (or local path)\n", "if DATA_DIR is not None:\n", "    DRIVE_MODELS = DRIVE_BASE / 'models'\n", "    DRIVE_MODELS.mkdir(parents=True, exist_ok=True)\n", "    model_path = DRIVE_MODELS / 'plant_model.h5'\n", "    classes_path = DRIVE_MODELS / 'classes.json'\n", "    model.save(str(model_path))\n", "    import json\n", "    with open(str(classes_path), 'w', encoding='utf-8') as fh:\n", "        json.dump({i: n for i, n in enumerate(class_names)}, fh, ensure_ascii=False, indent=2)\n", "    print('Saved model to', model_path)\n", "    print('Saved classes to', classes_path)\n", "else:\n", "    print('No model saved because DATA_DIR is None')\n"]}, {"cell_type": "code", "execution_count": null, "id": "6bcbcd9d", "metadata": {}, "outputs": [], "source": ["# Cell 10: Optional: Run train_transfer.py from notebook and capture output\n", "import subprocess, sys\n", "import os\n", "cwd = os.getcwd()\n", "print('cwd', cwd)\n", "train_script = os.path.join(cwd, 'train_transfer.py')\n", "if os.path.exists(train_script):\n", "    cmd = [sys.executable, train_script, '--data-dir', 'data/oneplant', '--out-model', 'models/plant_model.h5', '--out-classes', 'models/classes.json', '--epochs', '2']\n", "    print('Running:', cmd)\n", "    proc = subprocess.run(cmd, capture_output=True, text=True)\n", "    print(proc.stdout)\n", "    print(proc.stderr)\n", "else:\n", "    print('train_transfer.py not found in', cwd)\n"]}, {"cell_type": "markdown", "id": "a9807d5c", "metadata": {}, "source": ["## Troubleshooting and next steps\n", "\n", "- If `conda` is not available on Windows, install Miniconda and create a Python 3.10 environment. Use the commands in the notebook or the README.\n", "- If `pip install` fails building NumPy from source, prefer `conda install numpy` or use a Python with prebuilt wheels (3.8–3.11).\n", "- If TensorFlow can't be installed because of Python version mismatch, use Colab or create a conda env with Python 3.10.\n", "\n", "If you want, run the training cells in Colab now. After the model is saved in Drive, copy the produced `plant_model.h5` and `classes.json` back into `Medicinal-Plant-Backend/models/` in your local repo and restart the backend `serve.py` to enable real model predictions."]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}