{"version": 3, "names": ["_t", "require", "_helpersGenerated", "cloneNode", "identifier", "deep", "obj", "path", "value", "parts", "split", "last", "shift", "length", "arguments", "e", "message", "permuteHelperAST", "ast", "metadata", "bindingName", "localBindings", "getDependency", "adjustAst", "locals", "dependencies", "exportBindingAssignments", "exportName", "bindings", "Set", "add", "name", "paths", "Object", "entries", "o", "keys", "map", "k", "newName", "has", "ref", "for<PERSON>ach", "p", "helperData", "create", "loadHelper", "helper", "helpers", "assign", "ReferenceError", "code", "minVersion", "build", "nodes", "body", "globals", "getDependencies", "get", "id", "type", "undefined", "isInternal", "_helpers$name", "internal", "exports", "ensure", "list", "replace", "_default", "default"], "sources": ["../src/index.ts"], "sourcesContent": ["import { cloneNode, identifier } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport helpers from \"./helpers-generated.ts\";\nimport type { HelperMetadata } from \"./helpers-generated.ts\";\n\ntype GetDependency = (name: string) => t.Expression;\n\nfunction deep(obj: any, path: string, value?: unknown) {\n  try {\n    const parts = path.split(\".\");\n    let last = parts.shift();\n    while (parts.length > 0) {\n      obj = obj[last];\n      last = parts.shift();\n    }\n    if (arguments.length > 2) {\n      obj[last] = value;\n    } else {\n      return obj[last];\n    }\n  } catch (e) {\n    e.message += ` (when accessing ${path})`;\n    throw e;\n  }\n}\n\ntype AdjustAst = (\n  ast: t.Program,\n  exportName: string,\n  mapExportBindingAssignments: (\n    map: (node: t.Expression) => t.Expression,\n  ) => void,\n) => void;\n\n/**\n * Given a helper AST and information about how it will be used, update the AST to match the usage.\n */\nfunction permuteHelperAST(\n  ast: t.Program,\n  metadata: HelperMetadata,\n  bindingName: string | undefined,\n  localBindings: string[] | undefined,\n  getDependency: GetDependency | undefined,\n  adjustAst: AdjustAst | undefined,\n) {\n  const { locals, dependencies, exportBindingAssignments, exportName } =\n    metadata;\n\n  const bindings = new Set(localBindings || []);\n  if (bindingName) bindings.add(bindingName);\n  for (const [name, paths] of Object.entries(locals)) {\n    let newName = name;\n    if (bindingName && name === exportName) {\n      newName = bindingName;\n    } else {\n      while (bindings.has(newName)) newName = \"_\" + newName;\n    }\n\n    if (newName !== name) {\n      for (const path of paths) {\n        deep(ast, path, identifier(newName));\n      }\n    }\n  }\n\n  for (const [name, paths] of Object.entries(dependencies)) {\n    const ref =\n      (typeof getDependency === \"function\" && getDependency(name)) ||\n      identifier(name);\n    for (const path of paths) {\n      deep(ast, path, cloneNode(ref));\n    }\n  }\n\n  adjustAst?.(ast, exportName, map => {\n    exportBindingAssignments.forEach(p => deep(ast, p, map(deep(ast, p))));\n  });\n}\n\ninterface HelperData {\n  build: (\n    getDependency: GetDependency | undefined,\n    bindingName: string | undefined,\n    localBindings: string[] | undefined,\n    adjustAst: AdjustAst | undefined,\n  ) => {\n    nodes: t.Program[\"body\"];\n    globals: string[];\n  };\n  minVersion: string;\n  getDependencies: () => string[];\n}\n\nconst helperData: Record<string, HelperData> = Object.create(null);\nfunction loadHelper(name: string) {\n  if (!helperData[name]) {\n    const helper = helpers[name];\n    if (!helper) {\n      throw Object.assign(new ReferenceError(`Unknown helper ${name}`), {\n        code: \"BABEL_HELPER_UNKNOWN\",\n        helper: name,\n      });\n    }\n\n    helperData[name] = {\n      minVersion: helper.minVersion,\n      build(getDependency, bindingName, localBindings, adjustAst) {\n        const ast = helper.ast();\n        permuteHelperAST(\n          ast,\n          helper.metadata,\n          bindingName,\n          localBindings,\n          getDependency,\n          adjustAst,\n        );\n\n        return {\n          nodes: ast.body,\n          globals: helper.metadata.globals,\n        };\n      },\n      getDependencies() {\n        return Object.keys(helper.metadata.dependencies);\n      },\n    };\n  }\n\n  return helperData[name];\n}\n\nexport function get(\n  name: string,\n  getDependency?: GetDependency,\n  bindingName?: string,\n  localBindings?: string[],\n  adjustAst?: AdjustAst,\n) {\n  if (!process.env.BABEL_8_BREAKING) {\n    // In older versions, bindingName was a t.Identifier | t.MemberExpression\n    if (typeof bindingName === \"object\") {\n      const id = bindingName as t.Identifier | t.MemberExpression | null;\n      if (id?.type === \"Identifier\") {\n        bindingName = id.name;\n      } else {\n        bindingName = undefined;\n      }\n    }\n  }\n  return loadHelper(name).build(\n    getDependency,\n    bindingName,\n    localBindings,\n    adjustAst,\n  );\n}\n\nexport function minVersion(name: string) {\n  return loadHelper(name).minVersion;\n}\n\nexport function getDependencies(name: string): ReadonlyArray<string> {\n  return loadHelper(name).getDependencies();\n}\n\nexport function isInternal(name: string): boolean {\n  return helpers[name]?.metadata.internal;\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  // eslint-disable-next-line no-restricted-globals\n  exports.ensure = (name: string) => {\n    loadHelper(name);\n  };\n}\n\nexport const list = Object.keys(helpers).map(name => name.replace(/^_/, \"\"));\n\nexport default get;\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAEA,IAAAC,iBAAA,GAAAD,OAAA;AAA6C;EAFpCE,SAAS;EAAEC;AAAU,IAAAJ,EAAA;AAO9B,SAASK,IAAIA,CAACC,GAAQ,EAAEC,IAAY,EAAEC,KAAe,EAAE;EACrD,IAAI;IACF,MAAMC,KAAK,GAAGF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;IAC7B,IAAIC,IAAI,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC;IACxB,OAAOH,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MACvBP,GAAG,GAAGA,GAAG,CAACK,IAAI,CAAC;MACfA,IAAI,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC;IACtB;IACA,IAAIE,SAAS,CAACD,MAAM,GAAG,CAAC,EAAE;MACxBP,GAAG,CAACK,IAAI,CAAC,GAAGH,KAAK;IACnB,CAAC,MAAM;MACL,OAAOF,GAAG,CAACK,IAAI,CAAC;IAClB;EACF,CAAC,CAAC,OAAOI,CAAC,EAAE;IACVA,CAAC,CAACC,OAAO,IAAI,oBAAoBT,IAAI,GAAG;IACxC,MAAMQ,CAAC;EACT;AACF;AAaA,SAASE,gBAAgBA,CACvBC,GAAc,EACdC,QAAwB,EACxBC,WAA+B,EAC/BC,aAAmC,EACnCC,aAAwC,EACxCC,SAAgC,EAChC;EACA,MAAM;IAAEC,MAAM;IAAEC,YAAY;IAAEC,wBAAwB;IAAEC;EAAW,CAAC,GAClER,QAAQ;EAEV,MAAMS,QAAQ,GAAG,IAAIC,GAAG,CAACR,aAAa,IAAI,EAAE,CAAC;EAC7C,IAAID,WAAW,EAAEQ,QAAQ,CAACE,GAAG,CAACV,WAAW,CAAC;EAC1C,KAAK,MAAM,CAACW,IAAI,EAAEC,KAAK,CAAC,IAAI,CAAAC,MAAA,CAAAC,OAAA,KAAAC,CAAA,IAAAF,MAAA,CAAAG,IAAA,CAAAD,CAAA,EAAAE,GAAA,CAAAC,CAAA,KAAAA,CAAA,EAAAH,CAAA,CAAAG,CAAA,MAAed,MAAM,CAAC,EAAE;IAClD,IAAIe,OAAO,GAAGR,IAAI;IAClB,IAAIX,WAAW,IAAIW,IAAI,KAAKJ,UAAU,EAAE;MACtCY,OAAO,GAAGnB,WAAW;IACvB,CAAC,MAAM;MACL,OAAOQ,QAAQ,CAACY,GAAG,CAACD,OAAO,CAAC,EAAEA,OAAO,GAAG,GAAG,GAAGA,OAAO;IACvD;IAEA,IAAIA,OAAO,KAAKR,IAAI,EAAE;MACpB,KAAK,MAAMxB,IAAI,IAAIyB,KAAK,EAAE;QACxB3B,IAAI,CAACa,GAAG,EAAEX,IAAI,EAAEH,UAAU,CAACmC,OAAO,CAAC,CAAC;MACtC;IACF;EACF;EAEA,KAAK,MAAM,CAACR,IAAI,EAAEC,KAAK,CAAC,IAAI,CAAAC,MAAA,CAAAC,OAAA,KAAAC,CAAA,IAAAF,MAAA,CAAAG,IAAA,CAAAD,CAAA,EAAAE,GAAA,CAAAC,CAAA,KAAAA,CAAA,EAAAH,CAAA,CAAAG,CAAA,MAAeb,YAAY,CAAC,EAAE;IACxD,MAAMgB,GAAG,GACN,OAAOnB,aAAa,KAAK,UAAU,IAAIA,aAAa,CAACS,IAAI,CAAC,IAC3D3B,UAAU,CAAC2B,IAAI,CAAC;IAClB,KAAK,MAAMxB,IAAI,IAAIyB,KAAK,EAAE;MACxB3B,IAAI,CAACa,GAAG,EAAEX,IAAI,EAAEJ,SAAS,CAACsC,GAAG,CAAC,CAAC;IACjC;EACF;EAEAlB,SAAS,YAATA,SAAS,CAAGL,GAAG,EAAES,UAAU,EAAEU,GAAG,IAAI;IAClCX,wBAAwB,CAACgB,OAAO,CAACC,CAAC,IAAItC,IAAI,CAACa,GAAG,EAAEyB,CAAC,EAAEN,GAAG,CAAChC,IAAI,CAACa,GAAG,EAAEyB,CAAC,CAAC,CAAC,CAAC,CAAC;EACxE,CAAC,CAAC;AACJ;AAgBA,MAAMC,UAAsC,GAAGX,MAAM,CAACY,MAAM,CAAC,IAAI,CAAC;AAClE,SAASC,UAAUA,CAACf,IAAY,EAAE;EAChC,IAAI,CAACa,UAAU,CAACb,IAAI,CAAC,EAAE;IACrB,MAAMgB,MAAM,GAAGC,yBAAO,CAACjB,IAAI,CAAC;IAC5B,IAAI,CAACgB,MAAM,EAAE;MACX,MAAMd,MAAM,CAACgB,MAAM,CAAC,IAAIC,cAAc,CAAC,kBAAkBnB,IAAI,EAAE,CAAC,EAAE;QAChEoB,IAAI,EAAE,sBAAsB;QAC5BJ,MAAM,EAAEhB;MACV,CAAC,CAAC;IACJ;IAEAa,UAAU,CAACb,IAAI,CAAC,GAAG;MACjBqB,UAAU,EAAEL,MAAM,CAACK,UAAU;MAC7BC,KAAKA,CAAC/B,aAAa,EAAEF,WAAW,EAAEC,aAAa,EAAEE,SAAS,EAAE;QAC1D,MAAML,GAAG,GAAG6B,MAAM,CAAC7B,GAAG,CAAC,CAAC;QACxBD,gBAAgB,CACdC,GAAG,EACH6B,MAAM,CAAC5B,QAAQ,EACfC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SACF,CAAC;QAED,OAAO;UACL+B,KAAK,EAAEpC,GAAG,CAACqC,IAAI;UACfC,OAAO,EAAET,MAAM,CAAC5B,QAAQ,CAACqC;QAC3B,CAAC;MACH,CAAC;MACDC,eAAeA,CAAA,EAAG;QAChB,OAAOxB,MAAM,CAACG,IAAI,CAACW,MAAM,CAAC5B,QAAQ,CAACM,YAAY,CAAC;MAClD;IACF,CAAC;EACH;EAEA,OAAOmB,UAAU,CAACb,IAAI,CAAC;AACzB;AAEO,SAAS2B,GAAGA,CACjB3B,IAAY,EACZT,aAA6B,EAC7BF,WAAoB,EACpBC,aAAwB,EACxBE,SAAqB,EACrB;EACmC;IAEjC,IAAI,OAAOH,WAAW,KAAK,QAAQ,EAAE;MACnC,MAAMuC,EAAE,GAAGvC,WAAuD;MAClE,IAAI,CAAAuC,EAAE,oBAAFA,EAAE,CAAEC,IAAI,MAAK,YAAY,EAAE;QAC7BxC,WAAW,GAAGuC,EAAE,CAAC5B,IAAI;MACvB,CAAC,MAAM;QACLX,WAAW,GAAGyC,SAAS;MACzB;IACF;EACF;EACA,OAAOf,UAAU,CAACf,IAAI,CAAC,CAACsB,KAAK,CAC3B/B,aAAa,EACbF,WAAW,EACXC,aAAa,EACbE,SACF,CAAC;AACH;AAEO,SAAS6B,UAAUA,CAACrB,IAAY,EAAE;EACvC,OAAOe,UAAU,CAACf,IAAI,CAAC,CAACqB,UAAU;AACpC;AAEO,SAASK,eAAeA,CAAC1B,IAAY,EAAyB;EACnE,OAAOe,UAAU,CAACf,IAAI,CAAC,CAAC0B,eAAe,CAAC,CAAC;AAC3C;AAEO,SAASK,UAAUA,CAAC/B,IAAY,EAAW;EAAA,IAAAgC,aAAA;EAChD,QAAAA,aAAA,GAAOf,yBAAO,CAACjB,IAAI,CAAC,qBAAbgC,aAAA,CAAe5C,QAAQ,CAAC6C,QAAQ;AACzC;AAE+C;EAE7CC,OAAO,CAACC,MAAM,GAAInC,IAAY,IAAK;IACjCe,UAAU,CAACf,IAAI,CAAC;EAClB,CAAC;AACH;AAEO,MAAMoC,IAAI,GAAAF,OAAA,CAAAE,IAAA,GAAGlC,MAAM,CAACG,IAAI,CAACY,yBAAO,CAAC,CAACX,GAAG,CAACN,IAAI,IAAIA,IAAI,CAACqC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAJ,OAAA,CAAAK,OAAA,GAE9DZ,GAAG", "ignoreList": []}