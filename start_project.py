#!/usr/bin/env python3
"""
Simple script to start the medicinal plant project
"""

import subprocess
import time
import os
import requests
import webbrowser

def start_project():
    print("🚀 Starting Medicinal Plant Recognition System")
    print("=" * 50)
    
    # Change to backend directory and start backend
    backend_dir = os.path.join(os.getcwd(), "Medicinal-Plant-Backend")
    frontend_dir = os.path.join(os.getcwd(), "Medicinal-Plant-Web")
    
    print("1️⃣ Starting Backend Server...")
    try:
        backend_process = subprocess.Popen(
            ["python", "app.py"],
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        print("   ⏳ Backend starting...")
        time.sleep(3)
        
        # Test backend
        try:
            response = requests.get("http://localhost:5000/", timeout=5)
            if response.status_code == 200:
                print("   ✅ Backend running on http://localhost:5000")
            else:
                print(f"   ⚠️ Backend responded with status {response.status_code}")
        except:
            print("   ⚠️ Backend may still be starting...")
            
    except Exception as e:
        print(f"   ❌ Backend failed to start: {e}")
        return
    
    print("\n2️⃣ Starting Frontend Server...")
    try:
        frontend_process = subprocess.Popen(
            ["npm", "run", "dev"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        print("   ⏳ Frontend starting...")
        time.sleep(5)
        
        # Test frontend
        try:
            response = requests.get("http://localhost:5173/", timeout=5)
            if response.status_code == 200:
                print("   ✅ Frontend running on http://localhost:5173")
            else:
                print(f"   ⚠️ Frontend responded with status {response.status_code}")
        except:
            print("   ⚠️ Frontend may still be starting...")
            
    except Exception as e:
        print(f"   ❌ Frontend failed to start: {e}")
        backend_process.terminate()
        return
    
    print("\n3️⃣ Testing System...")
    time.sleep(2)
    
    # Test prediction API
    try:
        # Create minimal test image
        jpeg_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
        files = {'image': ('test.jpg', jpeg_data, 'image/jpeg')}
        
        pred_response = requests.post("http://localhost:5000/api/predict", files=files, timeout=10)
        if pred_response.status_code == 200:
            data = pred_response.json()
            print(f"   ✅ Prediction API working!")
            print(f"   🌿 Sample plant: {data.get('scientificName', 'Unknown')}")
            print(f"   📊 Confidence: {data.get('confidence', 0):.1%}")
        else:
            print(f"   ⚠️ Prediction API returned {pred_response.status_code}")
            
    except Exception as e:
        print(f"   ⚠️ Prediction test failed: {e}")
    
    print("\n" + "="*50)
    print("🎉 PROJECT IS RUNNING!")
    print("="*50)
    print("🌐 Web Interface: http://localhost:5173")
    print("📱 Backend API: http://localhost:5000")
    print("\n📋 How to use:")
    print("1. The web browser should open automatically")
    print("2. Upload any image file (JPG, PNG)")
    print("3. Click 'Analyze' to identify the plant")
    print("4. Explore all tabs for comprehensive information")
    print("\n⚠️ To stop the servers:")
    print("   Press Ctrl+C in this terminal")
    
    # Open web browser
    try:
        webbrowser.open("http://localhost:5173")
        print("\n🌐 Opening web browser...")
    except:
        print("\n🌐 Please open http://localhost:5173 in your browser")
    
    # Keep processes running
    try:
        print("\n⏳ Servers running... Press Ctrl+C to stop")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Stopping servers...")
        backend_process.terminate()
        frontend_process.terminate()
        print("✅ Servers stopped")

if __name__ == "__main__":
    start_project()
