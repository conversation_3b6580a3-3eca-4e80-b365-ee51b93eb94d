import csv
import json
import sys
import argparse
from pathlib import Path

def main():
    p = argparse.ArgumentParser(description="Create a class index JSON from a labels CSV file.")
    p.add_argument('--csv', type=Path, default=Path('labels.csv'), help="Path to the input labels.csv file.")
    p.add_argument('--out', type=Path, default=Path('models/classes.json'), help="Path for the output classes.json file.")
    args = p.parse_args()

    classes = []
    try:
        with open(args.csv, newline='', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                classes.append(row['class_name'])
    except FileNotFoundError:
        print(f"Error: Input file not found at {args.csv}", file=sys.stderr)
        sys.exit(1)

    # Ensure the output directory exists
    args.out.parent.mkdir(parents=True, exist_ok=True)
    # save mapping index->class (order preserved)
    mapping = {i: c for i, c in enumerate(classes)}
    with open(args.out, 'w', encoding='utf-8') as f:
        json.dump(mapping, f, indent=2)
    print(f'Wrote {len(classes)} classes to {args.out}')

if __name__ == '__main__':
    main()
