{"version": 3, "names": ["_assertThisInitialized", "require", "_possibleConstructorReturn", "self", "value", "TypeError", "assertThisInitialized"], "sources": ["../../src/helpers/possibleConstructorReturn.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport assertThisInitialized from \"./assertThisInitialized.ts\";\n\nexport default function _possibleConstructorReturn(\n  self: object | undefined,\n  value: unknown,\n) {\n  if (value && (typeof value === \"object\" || typeof value === \"function\")) {\n    return value;\n  } else if (value !== void 0) {\n    throw new TypeError(\n      \"Derived constructors may only return object or undefined\",\n    );\n  }\n\n  return assertThisInitialized(self);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,sBAAA,GAAAC,OAAA;AAEe,SAASC,0BAA0BA,CAChDC,IAAwB,EACxBC,KAAc,EACd;EACA,IAAIA,KAAK,KAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAU,CAAC,EAAE;IACvE,OAAOA,KAAK;EACd,CAAC,MAAM,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IAC3B,MAAM,IAAIC,SAAS,CACjB,0DACF,CAAC;EACH;EAEA,OAAO,IAAAC,8BAAqB,EAACH,IAAI,CAAC;AACpC", "ignoreList": []}