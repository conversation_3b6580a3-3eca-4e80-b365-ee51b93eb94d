#!/usr/bin/env python3
"""
YOLO Model Integration for Medicinal Plant Recognition
Integrates your trained YOLO model with the medicinal plant backend
"""

import os
import json
import yaml
import cv2
import numpy as np
from pathlib import Path
import logging
from PIL import Image
import io
import torch

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class YOLOPlantPredictor:
    """YOLO-based plant predictor for medicinal plants"""
    
    def __init__(self, model_path=None, data_yaml_path=None):
        """
        Initialize YOLO predictor
        
        Args:
            model_path: Path to YOLO model weights (.pt file)
            data_yaml_path: Path to data.yaml file with plant information
        """
        self.model_path = model_path
        self.data_yaml_path = data_yaml_path
        self.model = None
        self.plant_info = {}
        self.model_loaded = False
        
        # Your plant database from untitled0.py
        self.plant_names_mapping = {
            '<PERSON> ungu': {'scientific_name': 'Tradescantia spathacea', 'local_name': '<PERSON> ungu'},
            'Aglaonema': {'scientific_name': 'Aglaonema', 'local_name': 'Aglaonema'},
            'Anggrek': {'scientific_name': 'Orchidaceae', 'local_name': 'Anggrek'},
            'Cabai': {'scientific_name': 'Capsicum', 'local_name': 'Cabai'},
            'Daun bawang': {'scientific_name': 'Allium schoenoprasum', 'local_name': 'Daun bawang'},
            'Heliconia': {'scientific_name': 'Heliconia', 'local_name': 'Heliconia'},
            'Hydragea': {'scientific_name': 'Hydrangea', 'local_name': 'Hydrangea'},
            'Jengger ayam': {'scientific_name': 'Celosia cristata', 'local_name': 'Jengger ayam'},
            'Kana': {'scientific_name': 'Canna indica', 'local_name': 'Kana'},
            'Kembang Sepatu': {'scientific_name': 'Hibiscus rosa-sinensis', 'local_name': 'Kembang Sepatu'},
            'Kemuning': {'scientific_name': 'Murraya paniculata', 'local_name': 'Kemuning'},
            'Lidah Mertua': {'scientific_name': 'Dracaena trifasciata', 'local_name': 'Lidah Mertua'},
            'Marigold': {'scientific_name': 'Tagetes', 'local_name': 'Marigold'},
            'Matahari': {'scientific_name': 'Helianthus annuus', 'local_name': 'Matahari'},
            'Miana': {'scientific_name': 'Coleus scutellarioides', 'local_name': 'Miana'},
            'Pacar air': {'scientific_name': 'Impatiens balsamina', 'local_name': 'Pacar air'},
            'Patrakomala': {'scientific_name': 'Bougainvillea', 'local_name': 'Patrakomala'},
            'Pucuk Merah': {'scientific_name': 'Syzygium myrtifolium', 'local_name': 'Pucuk Merah'},
            'Puring': {'scientific_name': 'Codiaeum variegatum', 'local_name': 'Puring'},
            'Ruellia': {'scientific_name': 'Ruellia', 'local_name': 'Ruellia'},
            'Sawi': {'scientific_name': 'Brassica juncea', 'local_name': 'Sawi'},
            'Selada': {'scientific_name': 'Lactuca sativa', 'local_name': 'Selada'},
            'Telang': {'scientific_name': 'Clitoria ternatea', 'local_name': 'Telang'},
            'Thunbergia': {'scientific_name': 'Thunbergia alata', 'local_name': 'Thunbergia'},
            'Tomat': {'scientific_name': 'Solanum lycopersicum', 'local_name': 'Tomat'}
        }
        
        # Add medicinal uses for each plant
        self._add_medicinal_uses()
        
        # Try to load model
        self._load_model()
    
    def _add_medicinal_uses(self):
        """Add medicinal uses to plant information"""
        medicinal_uses = {
            'Adam Hawa ungu': 'Traditional ornamental plant, used for decorative purposes and air purification',
            'Aglaonema': 'Air purifying plant, used in traditional medicine for respiratory health',
            'Anggrek': 'Ornamental and medicinal, some species used in traditional healing',
            'Cabai': 'Rich in capsaicin, used for pain relief, circulation, and digestive health',
            'Daun bawang': 'Antimicrobial properties, used for respiratory infections and digestive issues',
            'Heliconia': 'Traditional use for wound healing and anti-inflammatory purposes',
            'Hydragea': 'Traditional use for kidney stones and urinary tract infections',
            'Jengger ayam': 'Used in traditional medicine for wound healing and skin conditions',
            'Kana': 'Traditional use for digestive issues and as anti-inflammatory agent',
            'Kembang Sepatu': 'Used for hair care, blood pressure management, and respiratory health',
            'Kemuning': 'Traditional use for fever, headaches, and skin conditions',
            'Lidah Mertua': 'Air purifying, traditional use for respiratory health and wound healing',
            'Marigold': 'Anti-inflammatory, wound healing, skin care, and eye health',
            'Matahari': 'Seeds used for cardiovascular health, anti-inflammatory properties',
            'Miana': 'Traditional use for respiratory issues, fever, and digestive problems',
            'Pacar air': 'Traditional use for skin conditions, wound healing, and hair dye',
            'Patrakomala': 'Traditional use for diabetes management and digestive health',
            'Pucuk Merah': 'Ornamental plant with traditional use for respiratory health',
            'Puring': 'Traditional use for skin conditions and as ornamental plant',
            'Ruellia': 'Traditional use for diabetes, hypertension, and kidney health',
            'Sawi': 'Rich in vitamins, used for immune support and digestive health',
            'Selada': 'Rich in nutrients, used for digestive health and hydration',
            'Telang': 'Antioxidant properties, used for eye health and cognitive function',
            'Thunbergia': 'Traditional use for skin conditions and wound healing',
            'Tomat': 'Rich in lycopene, used for cardiovascular health and antioxidant benefits'
        }
        
        # Add medicinal uses to plant mapping
        for plant_name in self.plant_names_mapping:
            self.plant_names_mapping[plant_name]['medicinal_uses'] = medicinal_uses.get(plant_name, 'Traditional medicinal plant with various therapeutic properties')
    
    def _load_model(self):
        """Load YOLO model"""
        try:
            # Try to import ultralytics
            from ultralytics import YOLO
            
            # Look for model files in common locations
            possible_paths = [
                self.model_path,
                "best.pt",
                "yolov8m.pt",
                "models/best.pt",
                "weights/best.pt",
                "runs/detect/train/weights/best.pt",
                "runs/detect/train5/weights/best.pt"
            ]
            
            model_found = False
            for path in possible_paths:
                if path and Path(path).exists():
                    self.model = YOLO(path)
                    self.model_path = path
                    model_found = True
                    logger.info(f"YOLO model loaded from: {path}")
                    break
            
            if not model_found:
                logger.warning("No YOLO model file found. Using mock predictions.")
                return False
            
            # Load data.yaml if available
            yaml_paths = [
                self.data_yaml_path,
                "data.yaml",
                "datasets/data.yaml"
            ]
            
            for yaml_path in yaml_paths:
                if yaml_path and Path(yaml_path).exists():
                    with open(yaml_path, 'r') as f:
                        data = yaml.safe_load(f)
                        if 'plant_info' in data:
                            self.plant_info = data['plant_info']
                        elif 'names' in data:
                            # Create plant info from names
                            for name in data['names']:
                                if name in self.plant_names_mapping:
                                    self.plant_info[name] = self.plant_names_mapping[name]
                    logger.info(f"Plant data loaded from: {yaml_path}")
                    break
            
            self.model_loaded = True
            return True
            
        except ImportError:
            logger.error("Ultralytics not installed. Install with: pip install ultralytics")
            return False
        except Exception as e:
            logger.error(f"Failed to load YOLO model: {e}")
            return False
    
    def preprocess_image(self, image_data):
        """
        Preprocess image for YOLO inference
        
        Args:
            image_data: Image data (bytes, PIL Image, or file path)
            
        Returns:
            Processed image ready for YOLO
        """
        try:
            # Handle different input types
            if isinstance(image_data, bytes):
                image = Image.open(io.BytesIO(image_data))
            elif isinstance(image_data, str):
                image = Image.open(image_data)
            elif isinstance(image_data, Image.Image):
                image = image_data
            else:
                raise ValueError("Unsupported image data type")
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            return image
            
        except Exception as e:
            logger.error(f"Image preprocessing failed: {e}")
            return None
    
    def predict_plant(self, image_data, confidence_threshold=0.5):
        """
        Predict plant species using YOLO model
        
        Args:
            image_data: Image data (bytes, PIL Image, or file path)
            confidence_threshold: Minimum confidence for predictions
            
        Returns:
            dict: Prediction results
        """
        if not self.model_loaded:
            return self._fallback_prediction()
        
        try:
            # Preprocess image
            image = self.preprocess_image(image_data)
            if image is None:
                return self._fallback_prediction()
            
            # Run YOLO inference
            results = self.model(image, conf=confidence_threshold)
            
            predictions = []
            
            # Process results
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Get class information
                        class_id = int(box.cls[0])
                        confidence = float(box.conf[0])
                        
                        # Get class name
                        class_name = self.model.names[class_id] if hasattr(self.model, 'names') else f"Class_{class_id}"
                        
                        # Get plant information
                        plant_info = self.plant_names_mapping.get(class_name, {})
                        
                        prediction = {
                            'plant_name': class_name,
                            'scientific_name': plant_info.get('scientific_name', 'Unknown'),
                            'local_name': plant_info.get('local_name', class_name),
                            'confidence': round(confidence * 100, 2),
                            'medicinal_uses': plant_info.get('medicinal_uses', 'Traditional medicinal plant'),
                            'bounding_box': {
                                'x1': float(box.xyxy[0][0]),
                                'y1': float(box.xyxy[0][1]),
                                'x2': float(box.xyxy[0][2]),
                                'y2': float(box.xyxy[0][3])
                            }
                        }
                        predictions.append(prediction)
            
            # Sort by confidence
            predictions.sort(key=lambda x: x['confidence'], reverse=True)
            
            # Format result
            result = {
                'success': True,
                'model_type': 'YOLO_v8',
                'predictions': predictions,
                'top_prediction': predictions[0] if predictions else None,
                'total_detections': len(predictions)
            }
            
            return result
            
        except Exception as e:
            logger.error(f"YOLO prediction failed: {e}")
            return self._fallback_prediction()
    
    def _fallback_prediction(self):
        """Fallback prediction when YOLO model fails"""
        import random
        
        # Return a random plant from our database
        plant_name = random.choice(list(self.plant_names_mapping.keys()))
        plant_info = self.plant_names_mapping[plant_name]
        
        return {
            'success': False,
            'model_type': 'fallback',
            'predictions': [{
                'plant_name': plant_name,
                'scientific_name': plant_info['scientific_name'],
                'local_name': plant_info['local_name'],
                'confidence': round(random.uniform(60, 85), 2),
                'medicinal_uses': plant_info['medicinal_uses']
            }],
            'top_prediction': {
                'plant_name': plant_name,
                'scientific_name': plant_info['scientific_name'],
                'local_name': plant_info['local_name'],
                'confidence': round(random.uniform(60, 85), 2),
                'medicinal_uses': plant_info['medicinal_uses']
            },
            'total_detections': 1
        }
    
    def get_model_info(self):
        """Get information about the loaded model"""
        return {
            'model_loaded': self.model_loaded,
            'model_path': str(self.model_path) if self.model_path else None,
            'model_type': 'YOLO_v8',
            'num_classes': len(self.plant_names_mapping),
            'classes': list(self.plant_names_mapping.keys()),
            'has_plant_info': len(self.plant_info) > 0
        }

# Global predictor instance
_yolo_predictor = None

def get_yolo_predictor():
    """Get global YOLO predictor instance"""
    global _yolo_predictor
    if _yolo_predictor is None:
        _yolo_predictor = YOLOPlantPredictor()
    return _yolo_predictor

def predict_plant_yolo(image_data, confidence_threshold=0.5):
    """
    Main YOLO prediction function for backend integration
    
    Args:
        image_data: Image data (bytes)
        confidence_threshold: Minimum confidence for predictions
        
    Returns:
        dict: Prediction results
    """
    predictor = get_yolo_predictor()
    return predictor.predict_plant(image_data, confidence_threshold)

def get_yolo_model_status():
    """Get YOLO model status for backend"""
    predictor = get_yolo_predictor()
    return predictor.get_model_info()

def create_yolo_data_yaml():
    """Create data.yaml file with your plant information"""
    predictor = get_yolo_predictor()
    
    data_yaml = {
        'path': './datasets',
        'train': 'train/images',
        'val': 'val/images',
        'test': 'test/images',
        'names': list(predictor.plant_names_mapping.keys()),
        'plant_info': predictor.plant_names_mapping
    }
    
    # Save data.yaml
    with open('data.yaml', 'w') as f:
        yaml.dump(data_yaml, f, default_flow_style=False)
    
    logger.info("data.yaml created with plant information")
    return data_yaml

def setup_yolo_environment():
    """Setup YOLO environment and dependencies"""
    try:
        # Install ultralytics if not available
        import subprocess
        import sys
        
        try:
            import ultralytics
            logger.info("Ultralytics already installed")
        except ImportError:
            logger.info("Installing ultralytics...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "ultralytics"])
            logger.info("Ultralytics installed successfully")
        
        # Create data.yaml
        create_yolo_data_yaml()
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to setup YOLO environment: {e}")
        return False

def main():
    """Main function to test YOLO integration"""
    print("🔍 YOLO Plant Detection Integration")
    print("=" * 40)
    
    # Setup environment
    print("🔧 Setting up YOLO environment...")
    if setup_yolo_environment():
        print("✅ Environment setup complete")
    else:
        print("❌ Environment setup failed")
        return
    
    # Initialize predictor
    print("\n🤖 Initializing YOLO predictor...")
    predictor = YOLOPlantPredictor()
    
    # Display model info
    model_info = predictor.get_model_info()
    print(f"📊 Model loaded: {model_info['model_loaded']}")
    print(f"🔢 Classes: {model_info['num_classes']}")
    print(f"🧠 Model type: {model_info['model_type']}")
    
    if model_info['model_path']:
        print(f"📁 Model path: {model_info['model_path']}")
    
    # Display available plants
    print(f"\n🌿 Available Plants ({len(predictor.plant_names_mapping)}):")
    for i, (plant_name, info) in enumerate(predictor.plant_names_mapping.items(), 1):
        print(f"   {i:2d}. {plant_name}")
        print(f"       Scientific: {info['scientific_name']}")
        print(f"       Local: {info['local_name']}")
        if i >= 5:  # Show first 5 plants
            print(f"       ... and {len(predictor.plant_names_mapping) - 5} more")
            break
    
    # Test prediction if sample image available
    print("\n🧪 Testing prediction...")
    
    # Look for sample images
    sample_dirs = [
        "test_images",
        "sample_images", 
        "images",
        "Medicinal-Plant-Web/public"
    ]
    
    sample_image = None
    for sample_dir in sample_dirs:
        sample_path = Path(sample_dir)
        if sample_path.exists():
            for img_file in sample_path.glob("*.jpg"):
                sample_image = img_file
                break
            if not sample_image:
                for img_file in sample_path.glob("*.png"):
                    sample_image = img_file
                    break
            if sample_image:
                break
    
    if sample_image:
        print(f"📸 Testing with: {sample_image}")
        result = predictor.predict_plant(str(sample_image))
        
        if result['success'] and result['top_prediction']:
            top_pred = result['top_prediction']
            print(f"✅ Prediction successful!")
            print(f"   Plant: {top_pred['plant_name']}")
            print(f"   Scientific: {top_pred['scientific_name']}")
            print(f"   Local: {top_pred['local_name']}")
            print(f"   Confidence: {top_pred['confidence']:.1f}%")
            print(f"   Uses: {top_pred['medicinal_uses'][:100]}...")
        else:
            print(f"⚠️ Using fallback prediction")
            if result['top_prediction']:
                top_pred = result['top_prediction']
                print(f"   Plant: {top_pred['plant_name']}")
                print(f"   Confidence: {top_pred['confidence']:.1f}%")
    else:
        print("⚠️ No sample images found for testing")
        print("   You can test by placing an image in the project directory")
    
    print("\n🎉 YOLO Integration Ready!")
    print("=" * 30)
    print("📋 Next Steps:")
    print("1. Copy your trained YOLO model (best.pt) to the project directory")
    print("2. Update backend integration: python update_backend_yolo.py")
    print("3. Start the application: python start_project.py")
    print("4. Test with plant images!")
    
    print(f"\n🌐 Your YOLO model supports {len(predictor.plant_names_mapping)} plant species:")
    print("   Including scientific names, local names, and medicinal uses!")

if __name__ == "__main__":
    main()
