#!/usr/bin/env python3
"""
AI Image Generation System for Medicinal Plants
Generates realistic plant images using Stable Diffusion and other AI models
"""

import os
import json
import requests
import time
from pathlib import Path
import random

class PlantImageGenerator:
    def __init__(self, output_dir="generated_dataset"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Load plant classes
        self.classes_file = "Medicinal-Plant-Backend/models/classes.json"
        with open(self.classes_file, 'r') as f:
            self.plant_classes = json.load(f)
        
        print(f"Loaded {len(self.plant_classes)} plant classes")
        
        # Image generation settings
        self.images_per_plant = 15  # Base images to generate
        self.image_size = 512
        
    def get_plant_prompts(self, scientific_name, local_name, description=None):
        """Generate diverse prompts for a plant species"""
        base_prompts = [
            f"High quality photograph of {scientific_name} ({local_name}) medicinal plant, detailed leaves, natural lighting, botanical photography",
            f"Close-up photo of {scientific_name} plant leaves, sharp focus, green foliage, medicinal herb",
            f"{local_name} plant in natural habitat, full plant view, botanical specimen, professional photography",
            f"Detailed macro photography of {scientific_name} leaves and stems, medicinal plant, high resolution",
            f"{local_name} medicinal herb, traditional medicine plant, clear botanical features, natural environment",
            f"Scientific botanical photograph of {scientific_name}, specimen quality, detailed plant structure",
            f"{local_name} plant with flowers, medicinal herb garden, natural lighting, botanical documentation",
            f"Professional photo of {scientific_name} plant, medicinal botany, clear leaf patterns, high quality",
            f"{local_name} herb in traditional medicine setting, natural plant photography, detailed foliage",
            f"Botanical illustration style photo of {scientific_name}, medicinal plant identification, clear features",
            f"Field photography of {local_name} plant, natural habitat, medicinal herb, botanical study",
            f"High resolution image of {scientific_name} leaves, medicinal plant botany, scientific photography",
            f"{local_name} plant specimen, traditional herbal medicine, detailed plant structure, natural lighting",
            f"Professional botanical photo of {scientific_name}, medicinal herb identification, clear details",
            f"Nature photography of {local_name} plant, medicinal botany, traditional healing herb, high quality"
        ]
        
        # Add description-based prompts if available
        if description and isinstance(description, dict):
            if description.get('appearance'):
                base_prompts.append(f"{scientific_name} plant, {description['appearance']}, medicinal herb photography")
            if description.get('habitat'):
                base_prompts.append(f"{local_name} in {description['habitat']}, natural medicinal plant photography")
        
        return base_prompts
    
    def generate_with_huggingface(self, prompt, plant_name, image_num):
        """Generate image using Hugging Face Inference API (free tier)"""
        try:
            # Using a free Stable Diffusion model
            API_URL = "https://api-inference.huggingface.co/models/runwayml/stable-diffusion-v1-5"
            
            # You can get a free API token from huggingface.co
            headers = {"Authorization": "Bearer YOUR_HF_TOKEN_HERE"}
            
            payload = {
                "inputs": prompt,
                "parameters": {
                    "num_inference_steps": 20,
                    "guidance_scale": 7.5,
                    "width": self.image_size,
                    "height": self.image_size
                }
            }
            
            response = requests.post(API_URL, headers=headers, json=payload)
            
            if response.status_code == 200:
                # Save image
                plant_dir = self.output_dir / plant_name
                plant_dir.mkdir(exist_ok=True)
                
                image_path = plant_dir / f"generated_{image_num:03d}.jpg"
                with open(image_path, "wb") as f:
                    f.write(response.content)
                
                print(f"✅ Generated: {image_path}")
                return str(image_path)
            else:
                print(f"❌ API Error: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Generation error: {e}")
            return None
    
    def generate_with_local_model(self, prompt, plant_name, image_num):
        """Generate image using local Stable Diffusion (if available)"""
        try:
            # This would use a local installation of Stable Diffusion
            # For now, we'll create a placeholder implementation
            print(f"🔄 Local generation: {prompt[:50]}...")
            
            # Placeholder - in real implementation, this would call:
            # - diffusers library
            # - local Stable Diffusion installation
            # - or other local AI models
            
            return None
            
        except Exception as e:
            print(f"❌ Local generation error: {e}")
            return None
    
    def download_reference_images(self, scientific_name, local_name, plant_name):
        """Download reference images from botanical databases (if available)"""
        try:
            # This could integrate with:
            # - PlantNet API
            # - iNaturalist API
            # - Botanical databases
            # - Wikipedia images
            
            print(f"🔍 Searching reference images for {scientific_name}...")
            
            # Placeholder for reference image collection
            # In real implementation, this would search and download
            # real plant images from open databases
            
            return []
            
        except Exception as e:
            print(f"❌ Reference download error: {e}")
            return []
    
    def generate_plant_dataset(self, plant_id=None, use_huggingface=False):
        """Generate complete dataset for one or all plants"""
        
        if plant_id:
            plants_to_process = {plant_id: self.plant_classes[plant_id]}
        else:
            plants_to_process = self.plant_classes
        
        total_generated = 0
        
        for plant_id, plant_data in plants_to_process.items():
            scientific_name = plant_data['scientific_name']
            local_name = plant_data['local_name']
            description = plant_data.get('description', {})
            
            print(f"\n🌿 Generating images for: {scientific_name} ({local_name})")
            
            # Create plant directory
            plant_name = scientific_name.lower().replace(' ', '_')
            plant_dir = self.output_dir / plant_name
            plant_dir.mkdir(exist_ok=True)
            
            # Generate prompts
            prompts = self.get_plant_prompts(scientific_name, local_name, description)
            
            # Generate base images
            generated_count = 0
            for i, prompt in enumerate(prompts[:self.images_per_plant]):
                if use_huggingface:
                    result = self.generate_with_huggingface(prompt, plant_name, i + 1)
                else:
                    result = self.generate_with_local_model(prompt, plant_name, i + 1)
                
                if result:
                    generated_count += 1
                
                # Rate limiting for API calls
                if use_huggingface:
                    time.sleep(2)  # Respect API limits
            
            # Download reference images
            reference_images = self.download_reference_images(scientific_name, local_name, plant_name)
            
            print(f"✅ Generated {generated_count} images for {scientific_name}")
            total_generated += generated_count
        
        print(f"\n🎉 Total images generated: {total_generated}")
        return total_generated

def main():
    print("🤖 AI Plant Image Generation System")
    print("=" * 50)
    
    generator = PlantImageGenerator()
    
    print("\nOptions:")
    print("1. Generate with Hugging Face API (requires token)")
    print("2. Generate with local model (requires setup)")
    print("3. Generate for specific plant")
    print("4. Show plant list")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == "1":
        print("\n⚠️ Note: You need a Hugging Face API token")
        print("Get one free at: https://huggingface.co/settings/tokens")
        confirm = input("Do you have a token configured? (y/n): ").strip().lower()
        if confirm == 'y':
            generator.generate_plant_dataset(use_huggingface=True)
        else:
            print("Please configure your Hugging Face token first")
    
    elif choice == "2":
        print("\n⚠️ Note: Local model generation requires:")
        print("- diffusers library")
        print("- torch/tensorflow")
        print("- Stable Diffusion model")
        generator.generate_plant_dataset(use_huggingface=False)
    
    elif choice == "3":
        print("\nAvailable plants:")
        for i, (plant_id, data) in enumerate(list(generator.plant_classes.items())[:10]):
            print(f"{plant_id}: {data['scientific_name']}")
        print("... (showing first 10)")
        
        plant_id = input("Enter plant ID: ").strip()
        if plant_id in generator.plant_classes:
            generator.generate_plant_dataset(plant_id=plant_id, use_huggingface=True)
        else:
            print("Invalid plant ID")
    
    elif choice == "4":
        print("\nAll plants in database:")
        for plant_id, data in generator.plant_classes.items():
            print(f"{plant_id}: {data['scientific_name']} ({data['local_name']})")
    
    else:
        print("Invalid choice")

if __name__ == "__main__":
    main()
