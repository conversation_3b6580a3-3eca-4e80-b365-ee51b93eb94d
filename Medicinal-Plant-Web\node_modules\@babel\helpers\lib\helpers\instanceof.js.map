{"version": 3, "names": ["_instanceof", "left", "right", "Symbol", "hasInstance"], "sources": ["../../src/helpers/instanceof.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _instanceof(left: any, right: Function) {\n  if (\n    right != null &&\n    typeof Symbol !== \"undefined\" &&\n    right[Symbol.hasInstance]\n  ) {\n    return !!right[Symbol.hasInstance](left);\n  } else {\n    return left instanceof right;\n  }\n}\n"], "mappings": ";;;;;;AAEe,SAASA,WAAWA,CAACC,IAAS,EAAEC,KAAe,EAAE;EAC9D,IACEA,KAAK,IAAI,IAAI,IACb,OAAOC,MAAM,KAAK,WAAW,IAC7BD,KAAK,CAACC,MAAM,CAACC,WAAW,CAAC,EACzB;IACA,OAAO,CAAC,CAACF,KAAK,CAACC,MAAM,CAACC,WAAW,CAAC,CAACH,IAAI,CAAC;EAC1C,CAAC,MAAM;IACL,OAAOA,IAAI,YAAYC,KAAK;EAC9B;AACF", "ignoreList": []}