"""generate_one_plant_augmented.py
Create an augmented dataset for a single plant class by transforming one source image
into many variants (rotations, flips, brightness changes) to simulate different angles.

Usage:
  python generate_one_plant_augmented.py --source data/sample/train/aloe_vera/aloe_vera_0.jpg --class aloe_vera --out data/oneplant --n_train 80 --n_val 20

This will create train and val folders, write labels.csv and models/classes.json.
"""
from PIL import Image, ImageEnhance
from pathlib import Path
import random
import argparse
import json
import os

IMG_SIZE = (224, 224)

def ensure(p):
    p.mkdir(parents=True, exist_ok=True)

def augment_and_save(img, outpath, seed=None):
    # img: PIL Image
    if seed is not None:
        random.seed(seed)
    im = img.copy()
    # random rotation between -40 and 40
    angle = random.uniform(-40, 40)
    im = im.rotate(angle, resample=Image.BILINEAR, expand=False)
    # random flip
    if random.random() < 0.5:
        im = im.transpose(Image.FLIP_LEFT_RIGHT)
    # random brightness
    enhancer = ImageEnhance.Brightness(im)
    im = enhancer.enhance(random.uniform(0.7, 1.3))
    # random crop & resize back
    w, h = im.size
    crop_frac = random.uniform(0.85, 1.0)
    cw, ch = int(w * crop_frac), int(h * crop_frac)
    left = random.randint(0, max(0, w - cw))
    top = random.randint(0, max(0, h - ch))
    im = im.crop((left, top, left + cw, top + ch)).resize(IMG_SIZE, Image.BILINEAR)
    im.save(outpath, format='JPEG')

def main():
    p = argparse.ArgumentParser()
    p.add_argument('--source', default='data/sample/train/aloe_vera/aloe_vera_0.jpg')
    p.add_argument('--class', dest='classname', default='aloe_vera')
    p.add_argument('--out', default='data/oneplant')
    p.add_argument('--n_train', type=int, default=80)
    p.add_argument('--n_val', type=int, default=20)
    args = p.parse_args()

    src = Path(args.source)
    if not src.exists():
        print('Source image not found:', src)
        return

    out_base = Path(args.out)
    train_dir = out_base / 'train' / args.classname
    val_dir = out_base / 'val' / args.classname
    ensure(train_dir)
    ensure(val_dir)

    img = Image.open(src).convert('RGB').resize(IMG_SIZE, Image.BILINEAR)

    # generate train images
    for i in range(args.n_train):
        outpath = train_dir / f'{args.classname}_{i}.jpg'
        augment_and_save(img, str(outpath), seed=i)

    # generate val images
    for i in range(args.n_val):
        outpath = val_dir / f'{args.classname}_val_{i}.jpg'
        augment_and_save(img, str(outpath), seed=1000 + i)

    # write labels.csv and models/classes.json for this dataset inside the output directory
    labels_out = out_base / 'labels.csv'
    with open(labels_out, 'w', encoding='utf-8') as fh:
        fh.write('class_name,scientificName,localName,medicinalFeature\n')
        fh.write(f"{args.classname},Exampleus plantus,Example Plant,simulated_features\n")

    models_dir = out_base / 'models'
    ensure(models_dir)
    with open(models_dir / 'classes.json', 'w', encoding='utf-8') as fh:
        json.dump({"0": args.classname}, fh, indent=2)

    print('Generated', args.n_train, 'train and', args.n_val, 'val images for', args.classname)
    print('Dataset written to', out_base)
    print(f'labels.csv and models/classes.json written in {out_base}')

if __name__ == '__main__':
    main()
