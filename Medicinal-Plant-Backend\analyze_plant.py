"""analyze_plant.py

Performs detailed image analysis on a single plant image to extract phenotyping traits.
This includes segmentation, leaf counting, dimensional analysis, and health assessment.

Functions:
    analyze_plant_image(image_path): Processes an image and returns analysis results.
"""
import cv2
import numpy as np
from skimage.feature import peak_local_max
from skimage.segmentation import watershed
from scipy import ndimage
import os

def analyze_plant_image(image_path, output_dir='data/analysis_results'):
    """
    Analyzes a plant image to extract key phenotyping details.

    Args:
        image_path (str): Path to the input image file.
        output_dir (str): Directory to save the annotated image.

    Returns:
        dict: A dictionary containing all extracted plant details and the path
              to the annotated image. Returns an error if no plant is detected.
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Read and resize image
    img = cv2.imread(image_path)
    if img is None:
        return {"error": "Image not found or could not be read."}
    
    # For consistent processing, resize the largest dimension to 1024px
    h, w, _ = img.shape
    scale = 1024 / max(h, w)
    img_resized = cv2.resize(img, (int(w * scale), int(h * scale)))

    # 1. Plant Segmentation
    hsv = cv2.cvtColor(img_resized, cv2.COLOR_BGR2HSV)
    
    # Define a broad range for green to capture various shades
    lower_green = np.array([25, 40, 40])
    upper_green = np.array([85, 255, 255])
    
    mask = cv2.inRange(hsv, lower_green, upper_green)
    
    # Clean up the mask with morphological operations
    kernel = np.ones((5, 5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=2)

    # Find the largest contour, assuming it's the plant
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        return {"error": "No plant detected in the image."}

    main_contour = max(contours, key=cv2.contourArea)
    plant_mask = np.zeros_like(mask)
    cv2.drawContours(plant_mask, [main_contour], -1, 255, -1)

    # 2. Plant Dimensions and Leaf Area
    x, y, w, h = cv2.boundingRect(main_contour)
    plant_height_px = h
    plant_width_px = w
    leaf_area_px = cv2.countNonZero(plant_mask)

    # 3. Leaf Counting (Approximation using Watershed)
    # This is a complex task; watershed provides a good approximation.
    D = ndimage.distance_transform_edt(plant_mask)
    local_max = peak_local_max(D, indices=False, min_distance=20, labels=plant_mask)
    markers = ndimage.label(local_max)[0]
    labels = watershed(-D, markers, mask=plant_mask)
    leaf_count = len(np.unique(labels)) - 1  # Subtract 1 for the background

    # 4. Health Analysis (Green/Yellow/Brown percentage)
    # Isolate the plant from the original image
    plant_only_img = cv2.bitwise_and(img_resized, img_resized, mask=plant_mask)
    plant_hsv = cv2.cvtColor(plant_only_img, cv2.COLOR_BGR2HSV)

    # Define color ranges for health status
    # Note: These ranges may need tuning for different lighting/species
    health_ranges = {
        "green": ([25, 40, 40], [85, 255, 255]),
        "yellow": ([15, 100, 100], [35, 255, 255]),
        "brown": ([5, 50, 20], [20, 200, 150])
    }

    health_pixels = {}
    for status, (lower, upper) in health_ranges.items():
        status_mask = cv2.inRange(plant_hsv, np.array(lower), np.array(upper))
        # Ensure we only count pixels within the main plant mask
        status_mask = cv2.bitwise_and(status_mask, status_mask, mask=plant_mask)
        health_pixels[status] = cv2.countNonZero(status_mask)

    total_plant_pixels = leaf_area_px
    health_percentages = {
        status: round((count / total_plant_pixels) * 100, 2) if total_plant_pixels > 0 else 0
        for status, count in health_pixels.items()
    }

    # 5. Create Annotated Image
    annotated_img = img_resized.copy()
    # Draw plant outline
    cv2.drawContours(annotated_img, [main_contour], -1, (255, 0, 255), 3)
    
    # Label each detected leaf segment
    for label in np.unique(labels):
        if label == 0:  # Background
            continue
        
        leaf_mask = np.zeros(plant_mask.shape, dtype="uint8")
        leaf_mask[labels == label] = 255
        
        # Find contour of the leaf to find its center
        cnts, _ = cv2.findContours(leaf_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if cnts:
            c = max(cnts, key=cv2.contourArea)
            M = cv2.moments(c)
            if M["m00"] > 0:
                cX = int(M["m10"] / M["m00"])
                cY = int(M["m01"] / M["m00"])
                cv2.putText(annotated_img, f"#{label}", (cX - 10, cY),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

    # Save the annotated image
    filename = os.path.basename(image_path)
    annotated_filename = f"annotated_{filename}"
    annotated_path = os.path.join(output_dir, annotated_filename)
    cv2.imwrite(annotated_path, annotated_img)

    # 6. Return structured JSON results
    results = {
        "plant_dimensions": {
            "height_px": plant_height_px,
            "width_px": plant_width_px,
            "unit": "pixels"
        },
        "leaf_area_px": leaf_area_px,
        "leaf_count_approx": leaf_count,
        "health_analysis_percent": health_percentages,
        "annotated_image_path": annotated_path
    }

    return results

if __name__ == '__main__':
    # Example usage for testing the script directly
    # Create a dummy image to test if none exists
    if not os.path.exists('data/sample/train/aloe_vera/aloe_vera_0.jpg'):
        print("Test image not found. Please generate the sample dataset first.")
    else:
        analysis_results = analyze_plant_image('data/sample/train/aloe_vera/aloe_vera_0.jpg')
        import json
        print(json.dumps(analysis_results, indent=2))