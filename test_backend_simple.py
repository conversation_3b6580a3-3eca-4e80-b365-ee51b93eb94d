#!/usr/bin/env python3
"""
Simple test to check if backend is working
"""

import subprocess
import time
import requests
import os

def test_backend():
    print("🔧 Testing Backend Functionality")
    print("=" * 40)
    
    # Change to backend directory
    backend_dir = "Medicinal-Plant-Backend"
    
    try:
        # Start the backend server
        print("Starting backend server...")
        process = subprocess.Popen(
            ["python", "app.py"],
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Test if server is responding
        try:
            response = requests.get("http://localhost:5000/", timeout=5)
            print(f"✅ Server Status: {response.status_code}")
            print(f"✅ Response: {response.json()}")
            
            # Test prediction endpoint with a minimal valid JPEG
            # Create a minimal valid JPEG image
            jpeg_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
            files = {'image': ('test.jpg', jpeg_data, 'image/jpeg')}
            pred_response = requests.post("http://localhost:5000/api/predict", files=files, timeout=10)
            
            if pred_response.status_code == 200:
                data = pred_response.json()
                print(f"✅ Prediction successful!")
                print(f"   Plant: {data.get('scientificName', 'Unknown')}")
                print(f"   Confidence: {data.get('confidence', 0)}")
                print(f"   Has description: {'description' in data}")
                print(f"   Has traditional systems: {'traditionalSystems' in data}")
                print(f"   Has preparation methods: {'preparationMethods' in data}")
                print(f"   Has safety info: {'safetyInfo' in data}")
                print(f"   Has geographical info: {'geographicalDistribution' in data}")
            else:
                print(f"❌ Prediction failed: {pred_response.status_code}")
                print(f"   Error: {pred_response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Connection failed: {e}")
            
        # Stop the server
        process.terminate()
        process.wait()
        
    except Exception as e:
        print(f"❌ Backend test failed: {e}")

if __name__ == "__main__":
    test_backend()
