{"version": 3, "names": ["_t", "require", "createFlowUnionType", "createTSUnionType", "createUnionTypeAnnotation", "isFlowType", "isTSType", "createUnionType", "types", "every", "v"], "sources": ["../../../src/path/inference/util.ts"], "sourcesContent": ["import {\n  createFlowUnionType,\n  createTSUnionType,\n  createUnionTypeAnnotation,\n  isFlowType,\n  isTSType,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nexport function createUnionType(\n  types: (t.FlowType | t.TSType)[],\n): t.FlowType | t.TSType | undefined {\n  if (process.env.BABEL_8_BREAKING) {\n    if (types.every(v => isFlowType(v))) {\n      return createFlowUnionType(types);\n    }\n    if (types.every(v => isTSType(v))) {\n      return createTSUnionType(types);\n    }\n  } else {\n    if (types.every(v => isFlowType(v))) {\n      if (createFlowUnionType) {\n        return createFlowUnionType(types);\n      }\n\n      return createUnionTypeAnnotation(types);\n    } else if (types.every(v => isTSType(v))) {\n      if (createTSUnionType) {\n        return createTSUnionType(types);\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAMsB;EALpBC,mBAAmB;EACnBC,iBAAiB;EACjBC,yBAAyB;EACzBC,UAAU;EACVC;AAAQ,IAAAN,EAAA;AAIH,SAASO,eAAeA,CAC7BC,KAAgC,EACG;EAQ5B;IACL,IAAIA,KAAK,CAACC,KAAK,CAACC,CAAC,IAAIL,UAAU,CAACK,CAAC,CAAC,CAAC,EAAE;MACnC,IAAIR,mBAAmB,EAAE;QACvB,OAAOA,mBAAmB,CAACM,KAAK,CAAC;MACnC;MAEA,OAAOJ,yBAAyB,CAACI,KAAK,CAAC;IACzC,CAAC,MAAM,IAAIA,KAAK,CAACC,KAAK,CAACC,CAAC,IAAIJ,QAAQ,CAACI,CAAC,CAAC,CAAC,EAAE;MACxC,IAAIP,iBAAiB,EAAE;QACrB,OAAOA,iBAAiB,CAACK,KAAK,CAAC;MACjC;IACF;EACF;AACF", "ignoreList": []}