#!/usr/bin/env python3
"""
Copy YOLO Model from untitled0.py Training
Finds and copies your trained YOLO model to the project
"""

import os
import shutil
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_yolo_models():
    """Find YOLO model files in common locations"""
    print("🔍 Searching for YOLO model files...")
    
    # Common locations where YOLO models might be
    search_paths = [
        # Current directory and subdirectories
        ".",
        "./runs",
        "./runs/detect",
        "./runs/detect/train",
        "./runs/detect/train/weights",
        "./runs/detect/train5",
        "./runs/detect/train5/weights",
        
        # Parent directories (in case script is in subfolder)
        "..",
        "../runs",
        "../runs/detect",
        "../runs/detect/train/weights",
        "../runs/detect/train5/weights",
        
        # Common download locations
        "~/Downloads",
        "~/Desktop",
        
        # Google Drive locations (if synced)
        "~/Google Drive",
        "~/GoogleDrive",
        "C:/Users/<USER>/Google Drive",
        
        # Common project locations
        "~/Documents",
        "~/Projects",
        
        # Colab download locations
        "./files_to_download",
        "../files_to_download"
    ]
    
    # Model file patterns to look for
    model_patterns = [
        "best.pt",
        "yolov8*.pt",
        "yolov11*.pt",
        "last.pt"
    ]
    
    found_models = []
    
    for search_path in search_paths:
        try:
            path = Path(search_path).expanduser()
            if path.exists():
                for pattern in model_patterns:
                    for model_file in path.rglob(pattern):
                        if model_file.is_file():
                            found_models.append(model_file)
                            print(f"   📁 Found: {model_file}")
        except Exception as e:
            # Skip paths that cause errors
            continue
    
    return found_models

def find_data_yaml():
    """Find data.yaml files"""
    print("🔍 Searching for data.yaml files...")
    
    search_paths = [
        ".",
        "..",
        "./datasets",
        "../datasets",
        "~/Downloads",
        "~/Desktop"
    ]
    
    found_yaml = []
    
    for search_path in search_paths:
        try:
            path = Path(search_path).expanduser()
            if path.exists():
                for yaml_file in path.rglob("data.yaml"):
                    if yaml_file.is_file():
                        found_yaml.append(yaml_file)
                        print(f"   📄 Found: {yaml_file}")
        except Exception as e:
            continue
    
    return found_yaml

def copy_model_files(model_files, yaml_files):
    """Copy model files to project directory"""
    print("\n📋 Available YOLO Models:")
    
    if not model_files:
        print("❌ No YOLO model files found!")
        print("Please manually copy your trained model files:")
        print("   • best.pt (your trained model)")
        print("   • data.yaml (optional)")
        return False
    
    # Display found models
    for i, model_file in enumerate(model_files, 1):
        size = model_file.stat().st_size / (1024 * 1024)  # Size in MB
        print(f"   {i}. {model_file} ({size:.1f} MB)")
    
    # Let user choose which model to copy
    if len(model_files) == 1:
        selected_model = model_files[0]
        print(f"\n✅ Auto-selecting: {selected_model}")
    else:
        try:
            choice = input(f"\nSelect model to copy (1-{len(model_files)}): ").strip()
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(model_files):
                selected_model = model_files[choice_idx]
            else:
                print("❌ Invalid choice!")
                return False
        except ValueError:
            print("❌ Invalid input!")
            return False
    
    # Copy selected model
    target_model = Path("best.pt")
    try:
        shutil.copy2(selected_model, target_model)
        print(f"✅ Copied model: {selected_model} -> {target_model}")
    except Exception as e:
        print(f"❌ Failed to copy model: {e}")
        return False
    
    # Copy data.yaml if available
    if yaml_files:
        print(f"\n📋 Available data.yaml files:")
        for i, yaml_file in enumerate(yaml_files, 1):
            print(f"   {i}. {yaml_file}")
        
        if len(yaml_files) == 1:
            selected_yaml = yaml_files[0]
            print(f"✅ Auto-selecting: {selected_yaml}")
        else:
            try:
                choice = input(f"Select data.yaml to copy (1-{len(yaml_files)}, or 0 to skip): ").strip()
                if choice == "0":
                    print("⏭️ Skipping data.yaml copy")
                    return True
                
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(yaml_files):
                    selected_yaml = yaml_files[choice_idx]
                else:
                    print("❌ Invalid choice, skipping data.yaml")
                    return True
            except ValueError:
                print("❌ Invalid input, skipping data.yaml")
                return True
        
        # Copy selected yaml
        target_yaml = Path("data.yaml")
        try:
            shutil.copy2(selected_yaml, target_yaml)
            print(f"✅ Copied data.yaml: {selected_yaml} -> {target_yaml}")
        except Exception as e:
            print(f"⚠️ Failed to copy data.yaml: {e}")
    
    return True

def verify_copied_files():
    """Verify that files were copied correctly"""
    print("\n🔍 Verifying copied files...")
    
    # Check model file
    model_file = Path("best.pt")
    if model_file.exists():
        size = model_file.stat().st_size / (1024 * 1024)
        print(f"✅ Model file: {model_file} ({size:.1f} MB)")
    else:
        print("❌ Model file not found!")
        return False
    
    # Check data.yaml
    yaml_file = Path("data.yaml")
    if yaml_file.exists():
        print(f"✅ Data file: {yaml_file}")
        
        # Try to read and validate
        try:
            import yaml
            with open(yaml_file, 'r') as f:
                data = yaml.safe_load(f)
            
            if 'names' in data:
                print(f"   📊 Classes: {len(data['names'])}")
                print(f"   🌿 Sample plants: {data['names'][:3]}...")
            
            if 'plant_info' in data:
                print(f"   📋 Plant info available: {len(data['plant_info'])} plants")
        
        except Exception as e:
            print(f"   ⚠️ Could not validate data.yaml: {e}")
    else:
        print("⚠️ data.yaml not found (will be created automatically)")
    
    return True

def create_test_script():
    """Create a test script to verify YOLO model works"""
    test_script = '''#!/usr/bin/env python3
"""
Test YOLO Model
Quick test to verify your YOLO model works
"""

def test_yolo_model():
    try:
        from ultralytics import YOLO
        import numpy as np
        from PIL import Image
        
        print("🧪 Testing YOLO Model...")
        
        # Load model
        model = YOLO("best.pt")
        print("✅ Model loaded successfully!")
        
        # Create a test image
        test_image = Image.fromarray(np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8))
        
        # Run prediction
        results = model(test_image)
        print("✅ Model prediction successful!")
        
        # Check classes
        if hasattr(model, 'names'):
            print(f"📊 Model classes: {len(model.names)}")
            print(f"🌿 Sample classes: {list(model.names.values())[:5]}")
        
        print("🎉 YOLO model test passed!")
        return True
        
    except ImportError:
        print("❌ Ultralytics not installed. Run: pip install ultralytics")
        return False
    except Exception as e:
        print(f"❌ YOLO test failed: {e}")
        return False

if __name__ == "__main__":
    test_yolo_model()
'''
    
    with open("test_yolo_model.py", 'w') as f:
        f.write(test_script)
    
    print("✅ Created test script: test_yolo_model.py")

def main():
    """Main function"""
    print("📁 YOLO Model Copy Utility")
    print("=" * 30)
    print("This script will find and copy your trained YOLO model files")
    print("from your untitled0.py training to the current project.")
    print()
    
    # Find model files
    model_files = find_yolo_models()
    yaml_files = find_data_yaml()
    
    if not model_files:
        print("\n❌ No YOLO model files found automatically!")
        print("\n📋 Manual Steps:")
        print("1. Locate your trained model file (usually 'best.pt')")
        print("2. Copy it to this directory")
        print("3. Optionally copy 'data.yaml' as well")
        print("4. Run: python setup_yolo_project.py")
        
        manual_path = input("\nEnter path to your model file (or press Enter to skip): ").strip()
        if manual_path and Path(manual_path).exists():
            try:
                shutil.copy2(manual_path, "best.pt")
                print(f"✅ Copied: {manual_path} -> best.pt")
            except Exception as e:
                print(f"❌ Copy failed: {e}")
        return
    
    # Copy files
    if copy_model_files(model_files, yaml_files):
        print("\n🎉 Model files copied successfully!")
        
        # Verify files
        if verify_copied_files():
            print("\n✅ File verification passed!")
        
        # Create test script
        create_test_script()
        
        print("\n🚀 Next Steps:")
        print("1. Test your model: python test_yolo_model.py")
        print("2. Setup project: python setup_yolo_project.py")
        print("3. Start application: python start_yolo_project.py")
        
    else:
        print("\n❌ Failed to copy model files!")

if __name__ == "__main__":
    main()
