from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os
from datetime import datetime
import random

# Load environment variables
MODEL_PATH = os.getenv("MODEL_PATH", "models/plant_model.h5")
CLASSES_PATH = os.getenv("CLASSES_PATH", "models/classes.json")

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Global variables for model and classes
model = None
class_names = {}

# Load class details
try:
    with open(CLASSES_PATH, "r") as f:
        class_names = json.load(f)
    print(f"Loaded {len(class_names)} plant classes")
except Exception as e:
    print(f"Error loading classes: {e}")

# Since TensorFlow is not available, we'll use a mock prediction system
print("Using mock prediction system (TensorFlow not available)")
model = None

def preprocess_image(image_bytes):
    """Simple image validation - in a real app you'd process the image for ML"""
    # For now, just validate that it's a valid image
    try:
        from PIL import Image
        import io
        image = Image.open(io.BytesIO(image_bytes)).convert("RGB")
        return True  # Image is valid
    except Exception:
        return False  # Invalid image

@app.route("/")
def home():
    return jsonify({"message": "Medicinal Plant Recognition API is running 🚀"})

@app.route("/api/predict", methods=["POST"])
def predict():
    if "image" not in request.files:
        return jsonify({"error": "No image file uploaded"}), 400

    file = request.files["image"]
    if file.filename == '':
        return jsonify({"error": "No file selected"}), 400

    try:
        img_bytes = file.read()
        is_valid_image = preprocess_image(img_bytes)

        if not is_valid_image:
            return jsonify({"error": "Invalid image file"}), 400

        # Use mock prediction system (since TensorFlow is not available)
        # In a real implementation, you would use your trained model here

        # For demonstration, prefer plants with comprehensive data (95% chance)
        comprehensive_plants = [9, 13, 30]  # Aloe Vera, Andrographis, Turmeric
        if random.random() < 0.95:
            class_index = random.choice(comprehensive_plants)
        else:
            class_index = random.randint(0, len(class_names) - 1)

        confidence = round(random.uniform(0.6, 0.95), 3)

        plant_data = class_names[str(class_index)]

        # Extract feature names and usage info
        features_list = []
        most_used_features = []

        for feature in plant_data["features"]:
            features_list.append(feature["name"])
            if feature["usage_frequency"] in ["very_high", "high"]:
                most_used_features.append({
                    "name": feature["name"],
                    "usage": feature["usage_frequency"],
                    "description": feature["description"]
                })

        result = {
            "scientificName": plant_data["scientific_name"],
            "localName": plant_data["local_name"],
            "realName": plant_data["real_name"],
            "commonNames": plant_data["common_names"],
            "medicinalFeature": features_list,
            "medicinalDetails": plant_data["features"],
            "mostUsedMedicines": most_used_features,
            "primaryMedicine": plant_data["most_used_medicine"],

            # Enhanced comprehensive data
            "description": plant_data.get("description", {}),
            "traditionalSystems": plant_data.get("traditional_systems", {}),
            "preparationMethods": plant_data.get("preparation_methods", []),
            "safetyInfo": plant_data.get("safety_info", {}),
            "geographicalDistribution": plant_data.get("geographical_distribution", {}),

            "confidence": confidence,
            "label": plant_data["scientific_name"].lower().replace(" ", "_"),
            "_mock": True
        }

        return jsonify(result)

    except Exception as e:
        return jsonify({"error": f"Prediction failed: {str(e)}"}), 500

@app.route("/api/save", methods=["POST"])
def save_record():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Here you would typically save to a database
        # For now, we'll just log it and return success
        print(f"Saving record: {data}")

        # You can add database logic here later
        # For example: db.save_plant_record(data)

        return jsonify({"message": "Record saved successfully", "timestamp": datetime.now().isoformat()})

    except Exception as e:
        return jsonify({"error": f"Save failed: {str(e)}"}), 500

@app.route("/api/feedback", methods=["POST"])
def save_feedback():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No feedback data provided"}), 400

        # Here you would typically save feedback to a database
        # For now, we'll just log it and return success
        print(f"Feedback received: {data}")

        # You can add database logic here later
        # For example: db.save_feedback(data)

        return jsonify({"message": "Feedback saved successfully", "timestamp": datetime.now().isoformat()})

    except Exception as e:
        return jsonify({"error": f"Feedback save failed: {str(e)}"}), 500

if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=5000)
