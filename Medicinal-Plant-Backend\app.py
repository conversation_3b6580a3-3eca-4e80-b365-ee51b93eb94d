from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os
from datetime import datetime
import random

# Load environment variables
MODEL_PATH = os.getenv("MODEL_PATH", "models/plant_model.h5")
CLASSES_PATH = os.getenv("CLASSES_PATH", "models/classes.json")

# Get the directory of this script to build absolute paths
script_dir = os.path.dirname(os.path.abspath(__file__))
CLASSES_PATH = os.path.join(script_dir, "models", "classes.json")

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Global variables for model and classes
model = None
class_names = {}

# Load class details
try:
    with open(CLASSES_PATH, "r") as f:
        class_names = json.load(f)
    print(f"Loaded {len(class_names)} plant classes")
except Exception as e:
    print(f"Error loading classes: {e}")

# Since TensorFlow is not available, we'll use a mock prediction system
print("Using mock prediction system (TensorFlow not available)")
model = None

def preprocess_image(image_bytes):
    """Simple image validation - in a real app you'd process the image for ML"""
    # For now, just validate that it's a valid image
    try:
        from PIL import Image
        import io
        image = Image.open(io.BytesIO(image_bytes)).convert("RGB")
        return True  # Image is valid
    except Exception:
        return False  # Invalid image

@app.route("/")
def home():
    return jsonify({"message": "Medicinal Plant Recognition API is running 🚀"})

@app.route("/api/predict", methods=["POST"])
def predict():
    if "image" not in request.files:
        return jsonify({"error": "No image file uploaded"}), 400

    file = request.files["image"]
    if file.filename == '':
        return jsonify({"error": "No file selected"}), 400

    try:
        img_bytes = file.read()
        # Temporarily disable strict image validation for testing
        # is_valid_image = preprocess_image(img_bytes)
        # if not is_valid_image:
        #     return jsonify({"error": "Invalid image file"}), 400

        # For now, just check if we have some data
        if len(img_bytes) == 0:
            return jsonify({"error": "Empty image file"}), 400

        # Use improved mock prediction system (since TensorFlow is not available)
        # In a real implementation, you would use your trained model here

        # Improved mock system: prefer plants with comprehensive data
        # Plants with complete information (traditional systems, preparation methods, safety info)
        comprehensive_plants = []
        basic_plants = []

        for idx, plant_data in class_names.items():
            has_traditional = bool(plant_data.get("traditional_systems"))
            has_preparation = bool(plant_data.get("preparation_methods"))
            has_safety = bool(plant_data.get("safety_info"))

            if has_traditional and has_preparation and has_safety:
                comprehensive_plants.append(int(idx))
            else:
                basic_plants.append(int(idx))

        # 70% chance to select from comprehensive plants, 30% from basic plants
        if comprehensive_plants and random.random() < 0.7:
            class_index = random.choice(comprehensive_plants)
        else:
            class_index = random.choice(basic_plants) if basic_plants else random.randint(0, len(class_names) - 1)

        # More realistic confidence scores based on "image quality"
        # Simulate different confidence levels for different scenarios
        confidence_ranges = [
            (0.85, 0.95),  # High confidence (20% chance)
            (0.70, 0.84),  # Medium-high confidence (40% chance)
            (0.55, 0.69),  # Medium confidence (30% chance)
            (0.40, 0.54)   # Lower confidence (10% chance)
        ]

        range_weights = [0.2, 0.4, 0.3, 0.1]
        selected_range = random.choices(confidence_ranges, weights=range_weights)[0]
        confidence = round(random.uniform(selected_range[0], selected_range[1]), 3)

        plant_data = class_names[str(class_index)]

        # Extract feature names and usage info
        features_list = []
        most_used_features = []

        for feature in plant_data["features"]:
            features_list.append(feature["name"])
            if feature["usage_frequency"] in ["very_high", "high"]:
                most_used_features.append({
                    "name": feature["name"],
                    "usage": feature["usage_frequency"],
                    "description": feature["description"]
                })

        result = {
            "scientificName": plant_data["scientific_name"],
            "localName": plant_data["local_name"],
            "realName": plant_data["real_name"],
            "commonNames": plant_data["common_names"],
            "medicinalFeature": features_list,
            "medicinalDetails": plant_data["features"],
            "mostUsedMedicines": most_used_features,
            "primaryMedicine": plant_data["most_used_medicine"],

            # Enhanced comprehensive data - ensure all fields are properly included
            "description": plant_data.get("description", {
                "appearance": "Detailed appearance information not available",
                "habitat": "Habitat information not available",
                "plant_parts_used": [],
                "active_compounds": []
            }),
            "traditionalSystems": plant_data.get("traditional_systems", {}),
            "preparationMethods": plant_data.get("preparation_methods", []),
            "safetyInfo": plant_data.get("safety_info", {
                "side_effects": [],
                "contraindications": [],
                "warnings": [],
                "toxicity_level": "Information not available"
            }),
            "geographicalDistribution": plant_data.get("geographical_distribution", {
                "native_regions": [],
                "cultivated_regions": [],
                "climate_zones": [],
                "altitude_range": "Information not available"
            }),

            "confidence": confidence,
            "label": plant_data["scientific_name"].lower().replace(" ", "_"),
            "_mock": True,
            "_note": "This is a mock prediction system. For accurate results, a trained ML model is needed."
        }

        return jsonify(result)

    except Exception as e:
        return jsonify({"error": f"Prediction failed: {str(e)}"}), 500

@app.route("/api/save", methods=["POST"])
def save_record():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Here you would typically save to a database
        # For now, we'll just log it and return success
        print(f"Saving record: {data}")

        # You can add database logic here later
        # For example: db.save_plant_record(data)

        return jsonify({"message": "Record saved successfully", "timestamp": datetime.now().isoformat()})

    except Exception as e:
        return jsonify({"error": f"Save failed: {str(e)}"}), 500

@app.route("/api/feedback", methods=["POST"])
def save_feedback():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No feedback data provided"}), 400

        # Here you would typically save feedback to a database
        # For now, we'll just log it and return success
        print(f"Feedback received: {data}")

        # You can add database logic here later
        # For example: db.save_feedback(data)

        return jsonify({"message": "Feedback saved successfully", "timestamp": datetime.now().isoformat()})

    except Exception as e:
        return jsonify({"error": f"Feedback save failed: {str(e)}"}), 500

if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=5000)
