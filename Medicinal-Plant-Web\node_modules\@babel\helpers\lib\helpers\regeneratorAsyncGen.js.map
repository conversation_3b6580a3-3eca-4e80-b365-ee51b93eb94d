{"version": 3, "names": ["_regenerator", "require", "_regeneratorAsyncIterator", "_regeneratorAsyncGen", "innerFn", "outerFn", "self", "tryLocsList", "PromiseImpl", "regeneratorAsyncIterator", "regenerator", "w", "Promise"], "sources": ["../../src/helpers/regeneratorAsyncGen.ts"], "sourcesContent": ["/* @minVersion 7.27.0 */\n/* @mangleFns */\n\nimport regenerator from \"./regenerator.ts\";\nimport regeneratorAsyncIterator from \"./regeneratorAsyncIterator.ts\";\n\nexport default /* @no-mangle */ function _regeneratorAsyncGen(\n  innerFn: Function,\n  outerFn: Function,\n  self: any,\n  tryLocsList: any[],\n  PromiseImpl: PromiseConstructor | undefined,\n) {\n  return new (regeneratorAsyncIterator as any)(\n    regenerator().w(innerFn as any, outerFn, self, tryLocsList),\n    PromiseImpl || Promise,\n  );\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,yBAAA,GAAAD,OAAA;AAEgC,SAASE,oBAAoBA,CAC3DC,OAAiB,EACjBC,OAAiB,EACjBC,IAAS,EACTC,WAAkB,EAClBC,WAA2C,EAC3C;EACA,OAAO,IAAKC,iCAAwB,CAClC,IAAAC,oBAAW,EAAC,CAAC,CAACC,CAAC,CAACP,OAAO,EAASC,OAAO,EAAEC,IAAI,EAAEC,WAAW,CAAC,EAC3DC,WAAW,IAAII,OACjB,CAAC;AACH", "ignoreList": []}