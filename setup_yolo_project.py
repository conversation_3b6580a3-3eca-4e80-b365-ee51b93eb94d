#!/usr/bin/env python3
"""
Complete YOLO Project Setup
Connects your YOLO training data to the medicinal plant project
"""

import os
import sys
import subprocess
import json
import yaml
import shutil
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class YOLOProjectSetup:
    """Complete setup for YOLO-powered medicinal plant recognition"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.steps_completed = []
        
        # Your plant database from untitled0.py
        self.plant_database = {
            'Adam Hawa ungu': {'scientific_name': 'Tradescantia spathacea', 'local_name': 'Adam <PERSON> ungu'},
            'Aglaonema': {'scientific_name': 'Aglaone<PERSON>', 'local_name': 'Aglaonema'},
            'Anggrek': {'scientific_name': 'Orchidaceae', 'local_name': '<PERSON><PERSON><PERSON>'},
            '<PERSON>abai': {'scientific_name': '<PERSON>si<PERSON>', 'local_name': '<PERSON><PERSON><PERSON>'},
            'Daun bawang': {'scientific_name': 'Allium schoenoprasum', 'local_name': 'Daun bawang'},
            'Heliconia': {'scientific_name': 'Heliconia', 'local_name': 'Heliconia'},
            'Hydragea': {'scientific_name': 'Hydrangea', 'local_name': 'Hydrangea'},
            'Jengger ayam': {'scientific_name': 'Celosia cristata', 'local_name': 'Jengger ayam'},
            'Kana': {'scientific_name': 'Canna indica', 'local_name': 'Kana'},
            'Kembang Sepatu': {'scientific_name': 'Hibiscus rosa-sinensis', 'local_name': 'Kembang Sepatu'},
            'Kemuning': {'scientific_name': 'Murraya paniculata', 'local_name': 'Kemuning'},
            'Lidah Mertua': {'scientific_name': 'Dracaena trifasciata', 'local_name': 'Lidah Mertua'},
            'Marigold': {'scientific_name': 'Tagetes', 'local_name': 'Marigold'},
            'Matahari': {'scientific_name': 'Helianthus annuus', 'local_name': 'Matahari'},
            'Miana': {'scientific_name': 'Coleus scutellarioides', 'local_name': 'Miana'},
            'Pacar air': {'scientific_name': 'Impatiens balsamina', 'local_name': 'Pacar air'},
            'Patrakomala': {'scientific_name': 'Bougainvillea', 'local_name': 'Patrakomala'},
            'Pucuk Merah': {'scientific_name': 'Syzygium myrtifolium', 'local_name': 'Pucuk Merah'},
            'Puring': {'scientific_name': 'Codiaeum variegatum', 'local_name': 'Puring'},
            'Ruellia': {'scientific_name': 'Ruellia', 'local_name': 'Ruellia'},
            'Sawi': {'scientific_name': 'Brassica juncea', 'local_name': 'Sawi'},
            'Selada': {'scientific_name': 'Lactuca sativa', 'local_name': 'Selada'},
            'Telang': {'scientific_name': 'Clitoria ternatea', 'local_name': 'Telang'},
            'Thunbergia': {'scientific_name': 'Thunbergia alata', 'local_name': 'Thunbergia'},
            'Tomat': {'scientific_name': 'Solanum lycopersicum', 'local_name': 'Tomat'}
        }
    
    def print_header(self):
        """Print setup header"""
        print("🔍" + "=" * 60 + "🔍")
        print("🤖 YOLO MEDICINAL PLANT PROJECT SETUP 🤖")
        print("🔍" + "=" * 60 + "🔍")
        print()
        print("🎯 CONNECTING YOUR YOLO TRAINING DATA TO THE PROJECT")
        print("📊 Your trained model: 25 plant species with scientific names")
        print("🌿 Target: Real AI plant detection with medicinal information")
        print()
    
    def check_prerequisites(self):
        """Check if all prerequisites are met"""
        print("🔍 Checking Prerequisites...")
        print("-" * 30)
        
        # Check Python version
        python_version = sys.version_info
        if python_version.major < 3 or python_version.minor < 8:
            print("❌ Python 3.8+ required")
            return False
        print(f"✅ Python {python_version.major}.{python_version.minor}")
        
        # Check project structure
        required_dirs = ["Medicinal-Plant-Backend", "Medicinal-Plant-Web"]
        missing_dirs = [d for d in required_dirs if not Path(d).exists()]
        
        if missing_dirs:
            print(f"❌ Missing directories: {missing_dirs}")
            return False
        print("✅ Project structure verified")
        
        # Check for YOLO files
        yolo_files = ["best.pt", "yolov8m.pt", "data.yaml"]
        found_files = [f for f in yolo_files if Path(f).exists()]
        
        if found_files:
            print(f"✅ YOLO files found: {found_files}")
        else:
            print("⚠️ No YOLO model files found in current directory")
            print("   Please copy your trained model files here")
        
        print()
        return True
    
    def install_dependencies(self):
        """Install required dependencies"""
        print("📦 Installing Dependencies...")
        print("-" * 30)
        
        dependencies = [
            "ultralytics",
            "opencv-python",
            "pillow",
            "pyyaml",
            "numpy",
            "torch",
            "torchvision"
        ]
        
        try:
            for dep in dependencies:
                print(f"Installing {dep}...")
                subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                             check=True, capture_output=True)
            
            print("✅ All dependencies installed successfully!")
            self.steps_completed.append("install_dependencies")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    
    def setup_yolo_files(self):
        """Setup YOLO model files"""
        print("📁 Setting up YOLO Files...")
        print("-" * 30)
        
        # Look for YOLO model files
        possible_paths = [
            "best.pt",
            "yolov8m.pt",
            "yolov11n.pt",
            "runs/detect/train/weights/best.pt",
            "runs/detect/train5/weights/best.pt",
            "../best.pt",
            "untitled0_files/best.pt"
        ]
        
        model_found = False
        for path in possible_paths:
            if Path(path).exists():
                target = Path("best.pt")
                if not target.exists():
                    shutil.copy2(path, target)
                    print(f"✅ Copied YOLO model: {path} -> {target}")
                else:
                    print(f"✅ YOLO model already exists: {target}")
                model_found = True
                break
        
        if not model_found:
            print("⚠️ No YOLO model found. Creating placeholder...")
            print("   Please copy your trained model (best.pt) to this directory")
        
        # Create data.yaml with your plant information
        self.create_data_yaml()
        
        self.steps_completed.append("setup_yolo_files")
        return True
    
    def create_data_yaml(self):
        """Create data.yaml file with plant information"""
        data_yaml = {
            'path': './datasets',
            'train': 'train/images',
            'val': 'val/images', 
            'test': 'test/images',
            'names': list(self.plant_database.keys()),
            'plant_info': self.plant_database
        }
        
        with open('data.yaml', 'w') as f:
            yaml.dump(data_yaml, f, default_flow_style=False)
        
        print("✅ Created data.yaml with your plant information")
    
    def create_plant_database(self):
        """Create comprehensive plant database"""
        print("🌿 Creating Plant Database...")
        print("-" * 30)
        
        # Add medicinal uses
        medicinal_uses = {
            'Adam Hawa ungu': 'Traditional ornamental plant, air purification, decorative purposes',
            'Aglaonema': 'Air purifying plant, respiratory health support in traditional medicine',
            'Anggrek': 'Ornamental and some medicinal species used in traditional healing',
            'Cabai': 'Rich in capsaicin, pain relief, circulation improvement, digestive health',
            'Daun bawang': 'Antimicrobial properties, respiratory infections, digestive support',
            'Heliconia': 'Traditional wound healing, anti-inflammatory properties',
            'Hydragea': 'Traditional use for kidney stones, urinary tract health',
            'Jengger ayam': 'Wound healing, skin conditions in traditional medicine',
            'Kana': 'Digestive support, anti-inflammatory properties',
            'Kembang Sepatu': 'Hair care, blood pressure management, respiratory health',
            'Kemuning': 'Fever reduction, headache relief, skin condition treatment',
            'Lidah Mertua': 'Air purification, respiratory health, wound healing',
            'Marigold': 'Anti-inflammatory, wound healing, skin care, eye health',
            'Matahari': 'Cardiovascular health from seeds, anti-inflammatory properties',
            'Miana': 'Respiratory issues, fever reduction, digestive problems',
            'Pacar air': 'Skin conditions, wound healing, natural hair dye',
            'Patrakomala': 'Diabetes management, digestive health support',
            'Pucuk Merah': 'Ornamental with respiratory health benefits',
            'Puring': 'Skin conditions, ornamental plant with traditional uses',
            'Ruellia': 'Diabetes management, hypertension, kidney health',
            'Sawi': 'Vitamin rich, immune support, digestive health',
            'Selada': 'Nutrient dense, digestive health, hydration support',
            'Telang': 'Antioxidant properties, eye health, cognitive function',
            'Thunbergia': 'Skin conditions, wound healing in traditional medicine',
            'Tomat': 'Lycopene rich, cardiovascular health, antioxidant benefits'
        }
        
        # Create comprehensive database
        plant_db = {}
        for i, (plant_name, info) in enumerate(self.plant_database.items()):
            plant_db[str(i)] = {
                "name": plant_name,
                "scientific_name": info['scientific_name'],
                "local_name": info['local_name'],
                "uses": medicinal_uses.get(plant_name, "Traditional medicinal plant"),
                "family": "Various",
                "description": f"{plant_name} ({info['scientific_name']}) - Traditional medicinal plant",
                "preparation": "Consult traditional medicine practitioners for proper preparation",
                "safety": "Use under guidance of qualified practitioners",
                "image": f"{plant_name.lower().replace(' ', '_')}.jpg"
            }
        
        # Save to backend
        models_dir = Path("Medicinal-Plant-Backend/models")
        models_dir.mkdir(exist_ok=True)
        
        with open(models_dir / "classes.json", 'w') as f:
            json.dump(plant_db, f, indent=2)
        
        print(f"✅ Created plant database with {len(plant_db)} species")
        self.steps_completed.append("create_plant_database")
        return True
    
    def integrate_backend(self):
        """Integrate YOLO with backend"""
        print("🔌 Integrating with Backend...")
        print("-" * 30)
        
        try:
            # Run the backend integration script
            from update_backend_yolo import update_backend_with_yolo
            
            if update_backend_with_yolo():
                print("✅ Backend integration successful!")
                self.steps_completed.append("integrate_backend")
                return True
            else:
                print("❌ Backend integration failed!")
                return False
                
        except Exception as e:
            print(f"❌ Backend integration error: {e}")
            return False
    
    def test_integration(self):
        """Test the complete integration"""
        print("🧪 Testing Integration...")
        print("-" * 25)
        
        try:
            from yolo_integration import get_yolo_predictor
            
            predictor = get_yolo_predictor()
            model_info = predictor.get_model_info()
            
            print(f"✅ YOLO predictor loaded")
            print(f"   Model loaded: {model_info['model_loaded']}")
            print(f"   Classes: {model_info['num_classes']}")
            print(f"   Model type: {model_info['model_type']}")
            
            # Test backend file
            backend_file = Path("Medicinal-Plant-Backend/app.py")
            if backend_file.exists():
                print("✅ Backend file updated")
            
            self.steps_completed.append("test_integration")
            return True
            
        except Exception as e:
            print(f"⚠️ Integration test warning: {e}")
            print("   (May work after starting the application)")
            return True
    
    def create_startup_script(self):
        """Create enhanced startup script"""
        print("🚀 Creating Startup Script...")
        print("-" * 30)
        
        startup_script = '''#!/usr/bin/env python3
"""
Enhanced Startup Script with YOLO Integration
"""

import subprocess
import sys
import time
from pathlib import Path

def start_backend():
    """Start Flask backend"""
    print("🔧 Starting Flask backend...")
    backend_dir = Path("Medicinal-Plant-Backend")
    
    if backend_dir.exists():
        return subprocess.Popen([
            sys.executable, "app.py"
        ], cwd=backend_dir)
    else:
        print("❌ Backend directory not found!")
        return None

def start_frontend():
    """Start React frontend"""
    print("🌐 Starting React frontend...")
    frontend_dir = Path("Medicinal-Plant-Web")
    
    if frontend_dir.exists():
        return subprocess.Popen([
            "npm", "run", "dev"
        ], cwd=frontend_dir)
    else:
        print("❌ Frontend directory not found!")
        return None

def main():
    print("🚀 Starting YOLO-Powered Medicinal Plant Recognition System")
    print("=" * 60)
    
    # Start backend
    backend_process = start_backend()
    if backend_process:
        print("✅ Backend started on http://localhost:5000")
    
    time.sleep(3)
    
    # Start frontend
    frontend_process = start_frontend()
    if frontend_process:
        print("✅ Frontend starting on http://localhost:5173")
    
    print("\\n🎉 Application is starting up!")
    print("📊 Features available:")
    print("   • YOLO plant detection")
    print("   • 25 plant species supported")
    print("   • Scientific names and medicinal uses")
    print("   • Real-time predictions")
    
    print("\\n🌐 Access your application:")
    print("   Frontend: http://localhost:5173")
    print("   Backend API: http://localhost:5000")
    print("   YOLO Status: http://localhost:5000/api/yolo-status")
    
    try:
        if backend_process:
            backend_process.wait()
        if frontend_process:
            frontend_process.wait()
    except KeyboardInterrupt:
        print("\\n⏹️ Shutting down...")
        if backend_process:
            backend_process.terminate()
        if frontend_process:
            frontend_process.terminate()

if __name__ == "__main__":
    main()
'''
        
        with open("start_yolo_project.py", 'w') as f:
            f.write(startup_script)
        
        print("✅ Created enhanced startup script: start_yolo_project.py")
        self.steps_completed.append("create_startup_script")
        return True
    
    def print_summary(self):
        """Print setup summary"""
        print("\n🎉 YOLO PROJECT SETUP COMPLETE!")
        print("=" * 45)
        
        print(f"✅ Completed steps: {len(self.steps_completed)}")
        for step in self.steps_completed:
            print(f"   • {step.replace('_', ' ').title()}")
        
        print("\n🌿 Your YOLO System Features:")
        print(f"   • {len(self.plant_database)} plant species")
        print("   • Scientific names and local names")
        print("   • Medicinal uses and properties")
        print("   • Real-time YOLO detection")
        print("   • Bounding box visualization")
        print("   • Confidence scores")
        
        print("\n🚀 How to Start:")
        print("   python start_yolo_project.py")
        
        print("\n🌐 API Endpoints:")
        print("   • POST /api/predict - Enhanced with YOLO")
        print("   • POST /api/yolo-predict - YOLO-specific")
        print("   • GET /api/yolo-status - Model status")
        
        print("\n📁 Files Created:")
        files = [
            "yolo_integration.py",
            "update_backend_yolo.py", 
            "data.yaml",
            "start_yolo_project.py",
            "Medicinal-Plant-Backend/models/classes.json"
        ]
        
        for file_path in files:
            if Path(file_path).exists():
                print(f"   ✅ {file_path}")
            else:
                print(f"   ❌ {file_path}")
        
        print("\n🎯 Next Steps:")
        print("1. Copy your trained YOLO model (best.pt) if not already done")
        print("2. Run: python start_yolo_project.py")
        print("3. Open: http://localhost:5173")
        print("4. Upload plant images to test YOLO detection!")
    
    def run_setup(self):
        """Run the complete setup"""
        self.print_header()
        
        if not self.check_prerequisites():
            print("❌ Prerequisites check failed!")
            return
        
        steps = [
            ("Install Dependencies", self.install_dependencies),
            ("Setup YOLO Files", self.setup_yolo_files),
            ("Create Plant Database", self.create_plant_database),
            ("Integrate Backend", self.integrate_backend),
            ("Test Integration", self.test_integration),
            ("Create Startup Script", self.create_startup_script)
        ]
        
        for step_name, step_func in steps:
            print(f"\n🔄 {step_name}...")
            try:
                if not step_func():
                    print(f"❌ {step_name} failed!")
                    break
            except KeyboardInterrupt:
                print(f"\n⏹️ Setup interrupted at: {step_name}")
                break
            except Exception as e:
                print(f"❌ Error in {step_name}: {e}")
                break
        
        self.print_summary()

def main():
    """Main function"""
    setup = YOLOProjectSetup()
    
    print("🤖 Welcome to YOLO Project Setup!")
    print("This will connect your YOLO training data to the medicinal plant project.")
    print()
    
    confirm = input("🚀 Start YOLO project setup? (y/n): ").strip().lower()
    
    if confirm == 'y':
        setup.run_setup()
    else:
        print("Setup cancelled")

if __name__ == "__main__":
    main()
