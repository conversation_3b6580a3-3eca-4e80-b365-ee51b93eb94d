#!/usr/bin/env python3
"""
Plant Classification Model Training
Trains a deep learning model for medicinal plant identification
"""

import os
import json
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms, models
from PIL import Image
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns

class PlantDataset(Dataset):
    def __init__(self, data_dir, transform=None):
        self.data_dir = Path(data_dir)
        self.transform = transform
        self.classes = sorted([d.name for d in self.data_dir.iterdir() if d.is_dir()])
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}
        
        # Load all image paths and labels
        self.samples = []
        for class_name in self.classes:
            class_dir = self.data_dir / class_name
            for img_path in class_dir.glob("*.jpg"):
                self.samples.append((str(img_path), self.class_to_idx[class_name]))
            for img_path in class_dir.glob("*.png"):
                self.samples.append((str(img_path), self.class_to_idx[class_name]))
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, label = self.samples[idx]
        image = Image.open(img_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        return image, label

class PlantClassifier(nn.Module):
    def __init__(self, num_classes, pretrained=True):
        super(PlantClassifier, self).__init__()
        
        # Use EfficientNet as backbone (good for plant classification)
        self.backbone = models.efficientnet_b0(pretrained=pretrained)
        
        # Replace classifier
        num_features = self.backbone.classifier[1].in_features
        self.backbone.classifier = nn.Sequential(
            nn.Dropout(0.3),
            nn.Linear(num_features, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, num_classes)
        )
    
    def forward(self, x):
        return self.backbone(x)

class PlantModelTrainer:
    def __init__(self, dataset_dir="training_dataset"):
        self.dataset_dir = Path(dataset_dir)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device: {self.device}")
        
        # Load dataset metadata
        metadata_path = self.dataset_dir / "dataset_metadata.json"
        if metadata_path.exists():
            with open(metadata_path, 'r') as f:
                self.metadata = json.load(f)
            self.num_classes = self.metadata["total_classes"]
        else:
            # Count classes manually
            train_dir = self.dataset_dir / "train"
            self.num_classes = len([d for d in train_dir.iterdir() if d.is_dir()])
        
        print(f"Number of classes: {self.num_classes}")
        
        # Data transforms
        self.train_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(15),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.val_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Training parameters
        self.config = {
            "batch_size": 32,
            "learning_rate": 0.001,
            "num_epochs": 50,
            "patience": 10,  # Early stopping
            "save_best_only": True
        }
    
    def create_data_loaders(self):
        """Create data loaders for training and validation"""
        # Create datasets
        train_dataset = PlantDataset(
            self.dataset_dir / "train",
            transform=self.train_transform
        )
        
        val_dataset = PlantDataset(
            self.dataset_dir / "val",
            transform=self.val_transform
        )
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config["batch_size"],
            shuffle=True,
            num_workers=4,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config["batch_size"],
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        self.class_names = train_dataset.classes
        print(f"Training samples: {len(train_dataset)}")
        print(f"Validation samples: {len(val_dataset)}")
        
        return train_loader, val_loader
    
    def train_model(self):
        """Train the plant classification model"""
        print("\n🚀 Starting Model Training")
        print("=" * 40)
        
        # Create data loaders
        train_loader, val_loader = self.create_data_loaders()
        
        # Create model
        model = PlantClassifier(self.num_classes, pretrained=True)
        model = model.to(self.device)
        
        # Loss function and optimizer
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=self.config["learning_rate"])
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
        
        # Training history
        history = {
            "train_loss": [],
            "train_acc": [],
            "val_loss": [],
            "val_acc": []
        }
        
        best_val_acc = 0.0
        patience_counter = 0
        
        for epoch in range(self.config["num_epochs"]):
            print(f"\nEpoch {epoch+1}/{self.config['num_epochs']}")
            print("-" * 30)
            
            # Training phase
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for batch_idx, (images, labels) in enumerate(train_loader):
                images, labels = images.to(self.device), labels.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(images)
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += labels.size(0)
                train_correct += (predicted == labels).sum().item()
                
                if batch_idx % 10 == 0:
                    print(f"Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}")
            
            train_acc = 100 * train_correct / train_total
            train_loss = train_loss / len(train_loader)
            
            # Validation phase
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            
            with torch.no_grad():
                for images, labels in val_loader:
                    images, labels = images.to(self.device), labels.to(self.device)
                    outputs = model(images)
                    loss = criterion(outputs, labels)
                    
                    val_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    val_total += labels.size(0)
                    val_correct += (predicted == labels).sum().item()
            
            val_acc = 100 * val_correct / val_total
            val_loss = val_loss / len(val_loader)
            
            # Update history
            history["train_loss"].append(train_loss)
            history["train_acc"].append(train_acc)
            history["val_loss"].append(val_loss)
            history["val_acc"].append(val_acc)
            
            print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%")
            print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%")
            
            # Learning rate scheduling
            scheduler.step(val_loss)
            
            # Early stopping and model saving
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                
                # Save best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_acc': val_acc,
                    'class_names': self.class_names
                }, 'best_plant_model.pth')
                
                print(f"✅ New best model saved! Val Acc: {val_acc:.2f}%")
            else:
                patience_counter += 1
                
                if patience_counter >= self.config["patience"]:
                    print(f"Early stopping triggered after {epoch+1} epochs")
                    break
        
        print(f"\n🎉 Training completed!")
        print(f"Best validation accuracy: {best_val_acc:.2f}%")
        
        # Save training history
        with open('training_history.json', 'w') as f:
            json.dump(history, f, indent=2)
        
        return model, history
    
    def evaluate_model(self, model_path="best_plant_model.pth"):
        """Evaluate the trained model on test set"""
        print("\n📊 Evaluating Model")
        print("=" * 25)
        
        # Load model
        checkpoint = torch.load(model_path, map_location=self.device)
        model = PlantClassifier(self.num_classes)
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(self.device)
        model.eval()
        
        # Create test dataset
        test_dataset = PlantDataset(
            self.dataset_dir / "test",
            transform=self.val_transform
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=self.config["batch_size"],
            shuffle=False,
            num_workers=4
        )
        
        # Evaluate
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for images, labels in test_loader:
                images, labels = images.to(self.device), labels.to(self.device)
                outputs = model(images)
                _, predicted = torch.max(outputs, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        # Calculate metrics
        accuracy = np.mean(np.array(all_predictions) == np.array(all_labels))
        print(f"Test Accuracy: {accuracy*100:.2f}%")
        
        # Classification report
        class_names = checkpoint['class_names']
        report = classification_report(all_labels, all_predictions, target_names=class_names)
        print("\nClassification Report:")
        print(report)
        
        # Save evaluation results
        with open('evaluation_results.txt', 'w') as f:
            f.write(f"Test Accuracy: {accuracy*100:.2f}%\n\n")
            f.write("Classification Report:\n")
            f.write(report)
        
        return accuracy

def main():
    print("🌿 Plant Classification Model Training")
    print("=" * 50)
    
    trainer = PlantModelTrainer()
    
    # Check if dataset exists
    if not trainer.dataset_dir.exists():
        print(f"❌ Dataset directory not found: {trainer.dataset_dir}")
        print("Please run the dataset generation script first")
        return
    
    print(f"Dataset directory: {trainer.dataset_dir}")
    print(f"Number of classes: {trainer.num_classes}")
    
    # Train model
    model, history = trainer.train_model()
    
    # Evaluate model
    accuracy = trainer.evaluate_model()
    
    print(f"\n🎉 Training Complete!")
    print(f"📊 Final test accuracy: {accuracy*100:.2f}%")
    print(f"💾 Model saved as: best_plant_model.pth")

if __name__ == "__main__":
    main()
