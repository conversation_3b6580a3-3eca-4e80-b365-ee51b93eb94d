"""train_transfer.py
Train a small transfer-learning model using MobileNetV2 on the sample dataset.
This script is designed to run locally where TensorFlow is installed.
"""
import json
import os
from pathlib import Path

def train(data_dir='data/sample', out_model='models/plant_model.h5', out_classes='models/classes.json', epochs=4):
    import tensorflow as tf
    from tensorflow.keras import layers, models

    data_dir = Path(__file__).parent / data_dir
    train_dir = data_dir / 'train'
    val_dir = data_dir / 'val'

    img_size = 224
    batch_size = 8

    train_ds = tf.keras.preprocessing.image_dataset_from_directory(
        train_dir, image_size=(img_size, img_size), batch_size=batch_size)
    val_ds = tf.keras.preprocessing.image_dataset_from_directory(
        val_dir, image_size=(img_size, img_size), batch_size=batch_size)

    class_names = train_ds.class_names
    num_classes = len(class_names)

    base_model = tf.keras.applications.MobileNetV2(input_shape=(img_size, img_size, 3), include_top=False, weights='imagenet')
    base_model.trainable = False

    inputs = tf.keras.Input(shape=(img_size, img_size, 3))
    x = tf.keras.applications.mobilenet_v2.preprocess_input(inputs)
    x = base_model(x, training=False)
    x = layers.GlobalAveragePooling2D()(x)
    x = layers.Dropout(0.2)(x)
    outputs = layers.Dense(num_classes, activation='softmax')(x)
    model = models.Model(inputs, outputs)

    model.compile(optimizer='adam', loss='sparse_categorical_crossentropy', metrics=['accuracy'])

    model.fit(train_ds, validation_data=val_ds, epochs=epochs)

    os.makedirs(Path(out_model).parent, exist_ok=True)
    model.save(out_model)

    with open(out_classes, 'w', encoding='utf-8') as f:
        json.dump({str(i): name for i, name in enumerate(class_names)}, f, indent=2)

    print('Model and classes saved:', out_model, out_classes)


if __name__ == '__main__':
    import argparse
    p = argparse.ArgumentParser(description='Train MobileNetV2 transfer model')
    p.add_argument('--data-dir', dest='data_dir', default='data/sample', help='Path to data directory (train/val)')
    p.add_argument('--out-model', dest='out_model', default='models/plant_model.h5', help='Output model path')
    p.add_argument('--out-classes', dest='out_classes', default='models/classes.json', help='Output classes json')
    p.add_argument('--epochs', type=int, default=4, help='Number of epochs')
    args = p.parse_args()
    train(data_dir=args.data_dir, out_model=args.out_model, out_classes=args.out_classes, epochs=args.epochs)
