#!/usr/bin/env python3
"""
Update Backend with YOLO Integration
Integrates your trained YOLO model into the medicinal plant backend
"""

import os
import json
from pathlib import Path
import logging
import shutil

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_backend_with_yolo():
    """Update the Flask backend to use YOLO model"""
    backend_file = Path("Medicinal-Plant-Backend/app.py")
    
    if not backend_file.exists():
        logger.error("Backend file not found")
        return False
    
    try:
        # Read current backend
        with open(backend_file, 'r') as f:
            content = f.read()
        
        # Check if already integrated
        if "yolo_integration" in content:
            logger.info("Backend already integrated with YOLO model")
            return True
        
        # Create backup
        backup_file = backend_file.with_suffix('.py.backup')
        shutil.copy2(backend_file, backup_file)
        logger.info(f"Backup created: {backup_file}")
        
        # Add YOLO import at the top
        import_line = "from yolo_integration import predict_plant_yolo, get_yolo_model_status\n"
        
        # Find the imports section
        lines = content.split('\n')
        import_index = 0
        for i, line in enumerate(lines):
            if line.startswith('import ') or line.startswith('from '):
                import_index = i + 1
        
        lines.insert(import_index, import_line.strip())
        
        # Add YOLO model status endpoint
        status_endpoint = '''
@app.route("/api/yolo-status", methods=["GET"])
def yolo_status():
    """Get YOLO model status"""
    try:
        status = get_yolo_model_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route("/api/yolo-predict", methods=["POST"])
def yolo_predict():
    """YOLO-specific prediction endpoint"""
    try:
        if "image" not in request.files:
            return jsonify({"error": "No image file provided"}), 400
        
        file = request.files["image"]
        if file.filename == "":
            return jsonify({"error": "No image file selected"}), 400
        
        # Read image data
        img_bytes = file.read()
        
        # Get confidence threshold from request
        confidence = float(request.form.get('confidence', 0.5))
        
        # Run YOLO prediction
        yolo_result = predict_plant_yolo(img_bytes, confidence)
        
        if yolo_result['success'] and yolo_result['predictions']:
            return jsonify({
                "success": True,
                "model_type": "YOLO_v8",
                "detections": yolo_result['total_detections'],
                "predictions": yolo_result['predictions'],
                "top_prediction": yolo_result['top_prediction']
            })
        else:
            return jsonify({
                "success": False,
                "model_type": "YOLO_fallback",
                "predictions": yolo_result['predictions'],
                "top_prediction": yolo_result['top_prediction']
            })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500
'''
        
        # Find where to add the endpoint (before the main prediction endpoint)
        predict_index = -1
        for i, line in enumerate(lines):
            if '@app.route("/api/predict"' in line:
                predict_index = i
                break
        
        if predict_index > 0:
            lines.insert(predict_index, status_endpoint.strip())
        
        # Update the main prediction endpoint to include YOLO
        updated_predict = '''
@app.route("/api/predict", methods=["POST"])
def predict():
    """Enhanced prediction with YOLO model"""
    try:
        if "image" not in request.files:
            return jsonify({"error": "No image file provided"}), 400
        
        file = request.files["image"]
        if file.filename == "":
            return jsonify({"error": "No image file selected"}), 400
        
        # Read image data
        img_bytes = file.read()
        
        # Try YOLO model first
        try:
            yolo_result = predict_plant_yolo(img_bytes, confidence_threshold=0.5)
            if yolo_result['success'] and yolo_result['predictions']:
                # Use YOLO prediction
                top_prediction = yolo_result['top_prediction']
                
                # Format response to match original structure
                response = {
                    "plant_name": top_prediction['plant_name'],
                    "scientific_name": top_prediction['scientific_name'],
                    "local_name": top_prediction['local_name'],
                    "confidence": f"{top_prediction['confidence']:.1f}%",
                    "medicinal_uses": top_prediction['medicinal_uses'],
                    "model_type": "YOLO_AI",
                    "detections": yolo_result['total_detections'],
                    "all_predictions": yolo_result['predictions']
                }
                
                # Add bounding box if available
                if 'bounding_box' in top_prediction:
                    response['bounding_box'] = top_prediction['bounding_box']
                
                return jsonify(response)
        except Exception as e:
            print(f"YOLO model failed: {e}")
        
        # Fallback to original mock system
        plant_id = random.choice(list(plant_classes.keys()))
        plant_info = plant_classes[plant_id]
        
        response = {
            "plant_name": plant_info["name"],
            "scientific_name": plant_info.get("scientific_name", "Unknown"),
            "local_name": plant_info.get("local_name", plant_info["name"]),
            "confidence": f"{random.uniform(60, 95):.1f}%",
            "medicinal_uses": plant_info.get("uses", "Traditional medicinal plant"),
            "model_type": "Mock_Fallback"
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500
'''
        
        # Replace the original predict function
        new_lines = []
        skip_lines = 0
        
        for i, line in enumerate(lines):
            if skip_lines > 0:
                skip_lines -= 1
                continue
                
            if '@app.route("/api/predict"' in line and 'yolo-predict' not in line:
                new_lines.append(updated_predict.strip())
                # Skip the original function
                for j in range(i + 1, len(lines)):
                    if lines[j].strip().startswith('@app.route') and j > i:
                        skip_lines = j - i - 1
                        break
                    elif lines[j].strip().startswith('if __name__') and j > i:
                        skip_lines = j - i - 1
                        break
                    elif j == len(lines) - 1:
                        skip_lines = j - i
                        break
            else:
                new_lines.append(line)
        
        # Write updated backend
        with open(backend_file, 'w') as f:
            f.write('\n'.join(new_lines))
        
        logger.info("Backend successfully integrated with YOLO model")
        return True
        
    except Exception as e:
        logger.error(f"Backend integration failed: {e}")
        return False

def copy_yolo_files():
    """Copy YOLO model files to appropriate locations"""
    logger.info("Looking for YOLO model files...")
    
    # Look for YOLO model files
    possible_model_paths = [
        "best.pt",
        "yolov8m.pt", 
        "yolov11n.pt",
        "runs/detect/train/weights/best.pt",
        "runs/detect/train5/weights/best.pt",
        "../best.pt",
        "../yolov8m.pt"
    ]
    
    model_found = False
    for model_path in possible_model_paths:
        if Path(model_path).exists():
            # Copy to project root if not already there
            target_path = Path("best.pt")
            if not target_path.exists():
                shutil.copy2(model_path, target_path)
                logger.info(f"Copied YOLO model: {model_path} -> {target_path}")
            model_found = True
            break
    
    if not model_found:
        logger.warning("No YOLO model file found. Please copy your trained model (best.pt) to the project directory.")
    
    # Look for data.yaml
    yaml_paths = [
        "data.yaml",
        "../data.yaml",
        "datasets/data.yaml"
    ]
    
    yaml_found = False
    for yaml_path in yaml_paths:
        if Path(yaml_path).exists():
            target_path = Path("data.yaml")
            if not target_path.exists():
                shutil.copy2(yaml_path, target_path)
                logger.info(f"Copied data.yaml: {yaml_path} -> {target_path}")
            yaml_found = True
            break
    
    if not yaml_found:
        logger.info("No data.yaml found. Will create one with your plant information.")
    
    return model_found

def create_yolo_classes_json():
    """Create classes.json file with YOLO plant information"""
    from yolo_integration import YOLOPlantPredictor
    
    predictor = YOLOPlantPredictor()
    
    # Convert YOLO plant mapping to classes.json format
    classes_data = {}
    
    for i, (plant_name, info) in enumerate(predictor.plant_names_mapping.items()):
        classes_data[str(i)] = {
            "name": plant_name,
            "scientific_name": info['scientific_name'],
            "local_name": info['local_name'],
            "uses": info['medicinal_uses'],
            "family": "Various",
            "description": f"{plant_name} ({info['scientific_name']}) - {info['medicinal_uses'][:100]}...",
            "preparation": "Consult traditional medicine practitioners for proper preparation methods",
            "safety": "Use under guidance of qualified practitioners",
            "image": f"{plant_name.lower().replace(' ', '_')}.jpg"
        }
    
    # Save to backend models directory
    models_dir = Path("Medicinal-Plant-Backend/models")
    models_dir.mkdir(exist_ok=True)
    
    classes_file = models_dir / "classes.json"
    with open(classes_file, 'w') as f:
        json.dump(classes_data, f, indent=2)
    
    logger.info(f"Created classes.json with {len(classes_data)} YOLO plants")
    return classes_file

def test_yolo_integration():
    """Test the YOLO integration"""
    try:
        from yolo_integration import get_yolo_predictor
        
        predictor = get_yolo_predictor()
        model_info = predictor.get_model_info()
        
        logger.info("YOLO Integration Test Results:")
        logger.info(f"  Model loaded: {model_info['model_loaded']}")
        logger.info(f"  Model type: {model_info['model_type']}")
        logger.info(f"  Number of classes: {model_info['num_classes']}")
        logger.info(f"  Model path: {model_info['model_path']}")
        
        return model_info['model_loaded']
        
    except Exception as e:
        logger.error(f"YOLO integration test failed: {e}")
        return False

def main():
    """Main function to update backend with YOLO"""
    print("🔄 Updating Backend with YOLO Integration")
    print("=" * 45)
    
    # Step 1: Copy YOLO files
    print("📁 Step 1: Copying YOLO model files...")
    model_found = copy_yolo_files()
    
    if model_found:
        print("✅ YOLO model files found and copied")
    else:
        print("⚠️ YOLO model files not found")
        print("   Please copy your trained model files:")
        print("   - best.pt (your trained YOLO model)")
        print("   - data.yaml (optional, will be created)")
    
    # Step 2: Create classes.json
    print("\n📊 Step 2: Creating plant database...")
    try:
        classes_file = create_yolo_classes_json()
        print(f"✅ Plant database created: {classes_file}")
    except Exception as e:
        print(f"❌ Failed to create plant database: {e}")
        return
    
    # Step 3: Update backend
    print("\n🔌 Step 3: Updating Flask backend...")
    if update_backend_with_yolo():
        print("✅ Backend integration successful!")
    else:
        print("❌ Backend integration failed!")
        return
    
    # Step 4: Test integration
    print("\n🧪 Step 4: Testing YOLO integration...")
    if test_yolo_integration():
        print("✅ YOLO integration test passed!")
    else:
        print("⚠️ YOLO integration test failed (may work after installing dependencies)")
    
    print("\n🎉 YOLO Backend Integration Complete!")
    print("=" * 45)
    
    print("📋 What's been updated:")
    print("✅ Flask backend now supports YOLO predictions")
    print("✅ New API endpoints added:")
    print("   • GET /api/yolo-status - YOLO model status")
    print("   • POST /api/yolo-predict - YOLO-specific predictions")
    print("   • POST /api/predict - Enhanced with YOLO support")
    print("✅ Plant database updated with your 25 plant species")
    print("✅ Scientific names and medicinal uses included")
    
    print("\n🚀 Next Steps:")
    print("1. Install YOLO dependencies: pip install ultralytics")
    print("2. Start the application: python start_project.py")
    print("3. Test with plant images!")
    print("4. Access enhanced API endpoints")
    
    print(f"\n🌿 Your system now supports {len(create_yolo_classes_json())} plant species:")
    print("   Including detection, scientific names, and medicinal uses!")

if __name__ == "__main__":
    main()
