#!/usr/bin/env python3
"""
Image Augmentation Pipeline for Plant Dataset
Applies various transformations to increase dataset diversity
"""

import os
import cv2
import numpy as np
from pathlib import Path
import random
from PIL import Image, ImageEnhance, ImageFilter
import albumentations as A
from albumentations.pytorch import ToTensorV2
import json

class PlantImageAugmenter:
    def __init__(self, input_dir="generated_dataset", output_dir="augmented_dataset"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Target images per class
        self.target_images_per_class = 100
        
        # Define augmentation pipeline
        self.augmentation_pipeline = A.Compose([
            # Geometric transformations
            A.RandomRotate90(p=0.3),
            <PERSON><PERSON>(limit=15, p=0.5),
            A.HorizontalFlip(p=0.5),
            A.VerticalFlip(p=0.2),
            A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.2, rotate_limit=15, p=0.5),
            
            # Color and lighting
            A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.5),
            <PERSON><PERSON>(hue_shift_limit=10, sat_shift_limit=20, val_shift_limit=20, p=0.5),
            A.RandomGamma(gamma_limit=(80, 120), p=0.3),
            A.CLAHE(clip_limit=2.0, tile_grid_size=(8, 8), p=0.3),
            
            # Noise and blur
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
            A.GaussianBlur(blur_limit=(1, 3), p=0.2),
            A.MotionBlur(blur_limit=3, p=0.2),
            
            # Weather effects
            A.RandomShadow(shadow_roi=(0, 0.5, 1, 1), num_shadows_lower=1, num_shadows_upper=2, p=0.3),
            A.RandomSunFlare(flare_roi=(0, 0, 1, 0.5), angle_lower=0, angle_upper=1, p=0.1),
            
            # Cropping and resizing
            A.RandomResizedCrop(height=512, width=512, scale=(0.8, 1.0), ratio=(0.8, 1.2), p=0.4),
            A.CenterCrop(height=480, width=480, p=0.2),
            
            # Final resize to standard size
            A.Resize(height=512, width=512, always_apply=True),
        ])
        
        # Specialized augmentations for botanical images
        self.botanical_augmentations = [
            self.add_natural_lighting,
            self.simulate_field_conditions,
            self.add_background_variation,
            self.simulate_camera_effects,
            self.add_seasonal_variation
        ]
    
    def add_natural_lighting(self, image):
        """Simulate natural lighting conditions"""
        # Convert to PIL for easier manipulation
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # Random lighting adjustments
        enhancer = ImageEnhance.Brightness(pil_image)
        brightness_factor = random.uniform(0.7, 1.3)
        pil_image = enhancer.enhance(brightness_factor)
        
        # Add warm/cool tint
        if random.random() < 0.3:
            enhancer = ImageEnhance.Color(pil_image)
            color_factor = random.uniform(0.8, 1.2)
            pil_image = enhancer.enhance(color_factor)
        
        return cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    
    def simulate_field_conditions(self, image):
        """Simulate real field photography conditions"""
        h, w = image.shape[:2]
        
        # Add slight motion blur (wind effect)
        if random.random() < 0.2:
            kernel_size = random.choice([3, 5])
            kernel = np.zeros((kernel_size, kernel_size))
            kernel[kernel_size//2, :] = 1.0 / kernel_size
            image = cv2.filter2D(image, -1, kernel)
        
        # Add depth of field effect
        if random.random() < 0.3:
            # Create a mask for focus area
            center_x, center_y = w//2, h//2
            radius = min(w, h) // 3
            
            mask = np.zeros((h, w), dtype=np.float32)
            cv2.circle(mask, (center_x, center_y), radius, 1.0, -1)
            mask = cv2.GaussianBlur(mask, (51, 51), 0)
            
            # Apply blur to background
            blurred = cv2.GaussianBlur(image, (15, 15), 0)
            mask = mask[:, :, np.newaxis]
            image = (image * mask + blurred * (1 - mask)).astype(np.uint8)
        
        return image
    
    def add_background_variation(self, image):
        """Add natural background variations"""
        h, w = image.shape[:2]
        
        # Add subtle texture overlay
        if random.random() < 0.2:
            # Create noise texture
            noise = np.random.randint(0, 30, (h, w, 3), dtype=np.uint8)
            alpha = 0.1
            image = cv2.addWeighted(image, 1-alpha, noise, alpha, 0)
        
        return image
    
    def simulate_camera_effects(self, image):
        """Simulate various camera effects"""
        # Add slight vignetting
        if random.random() < 0.3:
            h, w = image.shape[:2]
            
            # Create vignette mask
            X_resultant_kernel = cv2.getGaussianKernel(w, w/3)
            Y_resultant_kernel = cv2.getGaussianKernel(h, h/3)
            kernel = Y_resultant_kernel * X_resultant_kernel.T
            mask = kernel / kernel.max()
            
            # Apply vignette
            for i in range(3):
                image[:, :, i] = image[:, :, i] * mask
        
        return image
    
    def add_seasonal_variation(self, image):
        """Add seasonal color variations"""
        if random.random() < 0.3:
            # Simulate different seasons
            season = random.choice(['spring', 'summer', 'autumn', 'winter'])
            
            if season == 'autumn':
                # Add warmer tones
                image[:, :, 0] = np.clip(image[:, :, 0] * 0.9, 0, 255)  # Reduce blue
                image[:, :, 2] = np.clip(image[:, :, 2] * 1.1, 0, 255)  # Increase red
            elif season == 'winter':
                # Add cooler tones
                image[:, :, 0] = np.clip(image[:, :, 0] * 1.1, 0, 255)  # Increase blue
                image[:, :, 1] = np.clip(image[:, :, 1] * 0.95, 0, 255)  # Reduce green slightly
        
        return image
    
    def augment_image(self, image_path, num_augmentations=8):
        """Apply augmentations to a single image"""
        # Load image
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"❌ Could not load image: {image_path}")
            return []
        
        augmented_images = []
        
        for i in range(num_augmentations):
            # Apply albumentations pipeline
            augmented = self.augmentation_pipeline(image=image)['image']
            
            # Apply specialized botanical augmentations
            for aug_func in random.sample(self.botanical_augmentations, 2):
                if random.random() < 0.5:
                    augmented = aug_func(augmented)
            
            augmented_images.append(augmented)
        
        return augmented_images
    
    def process_plant_class(self, plant_dir):
        """Process all images in a plant class directory"""
        plant_name = plant_dir.name
        output_plant_dir = self.output_dir / plant_name
        output_plant_dir.mkdir(exist_ok=True)
        
        # Get all images in the directory
        image_files = list(plant_dir.glob("*.jpg")) + list(plant_dir.glob("*.png"))
        
        if not image_files:
            print(f"⚠️ No images found in {plant_dir}")
            return 0
        
        print(f"🔄 Processing {len(image_files)} images for {plant_name}")
        
        total_generated = 0
        
        # Calculate how many augmentations per image we need
        augmentations_per_image = max(1, self.target_images_per_class // len(image_files))
        
        for img_idx, image_path in enumerate(image_files):
            # Copy original image
            original_output = output_plant_dir / f"original_{img_idx:03d}.jpg"
            cv2.imwrite(str(original_output), cv2.imread(str(image_path)))
            total_generated += 1
            
            # Generate augmented versions
            augmented_images = self.augment_image(image_path, augmentations_per_image)
            
            for aug_idx, aug_image in enumerate(augmented_images):
                output_path = output_plant_dir / f"aug_{img_idx:03d}_{aug_idx:03d}.jpg"
                cv2.imwrite(str(output_path), aug_image)
                total_generated += 1
        
        print(f"✅ Generated {total_generated} images for {plant_name}")
        return total_generated
    
    def process_all_plants(self):
        """Process all plant classes in the input directory"""
        if not self.input_dir.exists():
            print(f"❌ Input directory not found: {self.input_dir}")
            return
        
        plant_dirs = [d for d in self.input_dir.iterdir() if d.is_dir()]
        
        if not plant_dirs:
            print(f"❌ No plant directories found in {self.input_dir}")
            return
        
        print(f"🚀 Processing {len(plant_dirs)} plant classes")
        
        total_images = 0
        for plant_dir in plant_dirs:
            count = self.process_plant_class(plant_dir)
            total_images += count
        
        print(f"\n🎉 Augmentation complete!")
        print(f"📊 Total images generated: {total_images}")
        print(f"📁 Output directory: {self.output_dir}")
        
        # Generate dataset summary
        self.generate_dataset_summary()
    
    def generate_dataset_summary(self):
        """Generate a summary of the augmented dataset"""
        summary = {
            "total_classes": 0,
            "total_images": 0,
            "classes": {}
        }
        
        for plant_dir in self.output_dir.iterdir():
            if plant_dir.is_dir():
                image_count = len(list(plant_dir.glob("*.jpg"))) + len(list(plant_dir.glob("*.png")))
                summary["classes"][plant_dir.name] = image_count
                summary["total_images"] += image_count
                summary["total_classes"] += 1
        
        # Save summary
        summary_path = self.output_dir / "dataset_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"📋 Dataset summary saved to: {summary_path}")

def main():
    print("🔄 Plant Image Augmentation Pipeline")
    print("=" * 50)
    
    augmenter = PlantImageAugmenter()
    
    print(f"Input directory: {augmenter.input_dir}")
    print(f"Output directory: {augmenter.output_dir}")
    print(f"Target images per class: {augmenter.target_images_per_class}")
    
    if not augmenter.input_dir.exists():
        print(f"❌ Input directory not found: {augmenter.input_dir}")
        print("Please run the image generation script first")
        return
    
    confirm = input("\nStart augmentation process? (y/n): ").strip().lower()
    if confirm == 'y':
        augmenter.process_all_plants()
    else:
        print("Augmentation cancelled")

if __name__ == "__main__":
    main()
