# Medicinal Plant Backend

This backend server provides APIs for medicinal plant species identification and phenotyping analysis.

## Project Workflow

The intended workflow is as follows:

1.  **Setup Environment**: Create a Python virtual environment and install dependencies.
2.  **Prepare Data**: Run the ingestion script to download images and create a sample dataset.
3.  **Train Model**: Train a transfer-learning model on the sample dataset.
4.  **Run Server**: Start the Flask API server.

## Step-by-Step Instructions

### 1. Environment Setup

This project requires **Python 3.11** because TensorFlow does not yet support newer versions. It is crucial to use a virtual environment created with Python 3.11.

```bash
# First, navigate to the project directory
cd c:\Users\<USER>\Documents\Medicinal\Medicinal-Plant-Backend\

# Create a virtual environment using Python 3.11
# NOTE: Replace the path below with your actual Python 3.11 installation path.
& "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" -m venv venv

# Activate the environment (in PowerShell)
.\venv\Scripts\Activate.ps1
# Or, in Windows Command Prompt (cmd.exe)
.\venv\Scripts\activate.bat

# If this command fails with a 'not recognized' error, it's likely the virtual
# environment was not created. Please run the creation command above first.

# After activation, your terminal prompt should be prefixed with `(venv)`.

# Verify the Python version inside the environment
python --version
# The output should show Python 3.11.x. If not, delete the 'venv' folder
# and re-run the creation command with the correct Python 3.11 path.

# Install required packages
pip install --upgrade pip
pip install -r requirements.txt

# (Optional) Verify that TensorFlow is installed
pip list | findstr tensorflow
# You should see tensorflow in the output list.
```

### 2. Data Preparation

The `ingest_frontend_csv.py` script downloads images and prepares a small `data/sample` dataset, which is used for training. It also generates `labels.csv` and `models/classes.json`.

```bash
# This assumes the frontend repo is in a sibling directory
python ingest_frontend_csv.py
```

### 3. Model Training

Train the MobileNetV2-based transfer learning model using the `train_transfer.py` script. This will create the `models/plant_model.h5` file.

```bash
python train_transfer.py --epochs 5
```

### 4. Run the API Server

Start the Flask server. It will load the trained model and provide API endpoints.

```bash
python serve.py
```

The server will be available at `http://localhost:5000`.

### API Endpoints

-   `POST /api/predict`: Upload an image for species classification.
-   `POST /api/analyze`: Upload an image for detailed phenotyping analysis.
-   `POST /api/feedback`: Submit user feedback on a prediction.
-   `GET /api/health`: Health check endpoint.
