#!/usr/bin/env python3
"""
Complete system test to verify all sections are working
"""

import requests
import json

def test_complete_system():
    print("🧪 Testing Complete Medicinal Plant System")
    print("=" * 50)
    
    # Test backend API
    try:
        # Test basic API status
        response = requests.get("http://localhost:5000/", timeout=5)
        print(f"✅ Backend Status: {response.status_code}")
        
        # Test prediction with comprehensive data check
        jpeg_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
        files = {'image': ('test.jpg', jpeg_data, 'image/jpeg')}
        
        pred_response = requests.post("http://localhost:5000/api/predict", files=files, timeout=10)
        
        if pred_response.status_code == 200:
            data = pred_response.json()
            print(f"✅ Prediction API working!")
            print(f"\n🌿 Plant Information:")
            print(f"   Scientific Name: {data.get('scientificName', 'N/A')}")
            print(f"   Local Name: {data.get('localName', 'N/A')}")
            print(f"   Real Name: {data.get('realName', 'N/A')}")
            print(f"   Common Names: {len(data.get('commonNames', []))} names")
            print(f"   Primary Medicine: {data.get('primaryMedicine', 'N/A')}")
            print(f"   Confidence: {data.get('confidence', 0):.1%}")
            
            print(f"\n📊 Data Completeness Check:")
            
            # Check Overview section
            description = data.get('description', {})
            print(f"   ✅ Description: {bool(description and description.get('appearance'))}")
            print(f"      - Appearance: {bool(description.get('appearance'))}")
            print(f"      - Habitat: {bool(description.get('habitat'))}")
            print(f"      - Plant Parts: {len(description.get('plant_parts_used', []))} parts")
            print(f"      - Active Compounds: {len(description.get('active_compounds', []))} compounds")
            
            # Check medicinal features
            medicinal_details = data.get('medicinalDetails', [])
            print(f"   ✅ Medicinal Features: {len(medicinal_details)} features")
            for feature in medicinal_details[:3]:  # Show first 3
                print(f"      - {feature.get('name', 'Unknown')}: {feature.get('usage_frequency', 'unknown')} usage")
            
            # Check Traditional Medicine section
            traditional = data.get('traditionalSystems', {})
            print(f"   ✅ Traditional Systems: {len(traditional)} systems")
            for system in traditional.keys():
                print(f"      - {system.title()}: {traditional[system].get('name', 'N/A')}")
            
            # Check Preparation section
            preparation = data.get('preparationMethods', [])
            print(f"   ✅ Preparation Methods: {len(preparation)} methods")
            for method in preparation[:2]:  # Show first 2
                print(f"      - {method.get('method', 'Unknown')}")
            
            # Check Safety section
            safety = data.get('safetyInfo', {})
            print(f"   ✅ Safety Information:")
            print(f"      - Side Effects: {len(safety.get('side_effects', []))} listed")
            print(f"      - Contraindications: {len(safety.get('contraindications', []))} listed")
            print(f"      - Warnings: {len(safety.get('warnings', []))} listed")
            print(f"      - Toxicity Level: {safety.get('toxicity_level', 'N/A')}")
            
            # Check Geography section
            geography = data.get('geographicalDistribution', {})
            print(f"   ✅ Geographical Information:")
            print(f"      - Native Regions: {len(geography.get('native_regions', []))} regions")
            print(f"      - Cultivated Regions: {len(geography.get('cultivated_regions', []))} regions")
            print(f"      - Climate Zones: {len(geography.get('climate_zones', []))} zones")
            print(f"      - Altitude Range: {geography.get('altitude_range', 'N/A')}")
            
            # Overall assessment
            sections_with_data = 0
            if description and description.get('appearance'): sections_with_data += 1
            if medicinal_details: sections_with_data += 1
            if traditional: sections_with_data += 1
            if preparation: sections_with_data += 1
            if safety and (safety.get('side_effects') or safety.get('contraindications')): sections_with_data += 1
            if geography and (geography.get('native_regions') or geography.get('cultivated_regions')): sections_with_data += 1
            
            print(f"\n🎯 Overall Assessment: {sections_with_data}/6 sections have comprehensive data")
            
            if sections_with_data >= 4:
                print("✅ EXCELLENT: Most sections are working properly!")
            elif sections_with_data >= 2:
                print("⚠️  GOOD: Some sections need improvement")
            else:
                print("❌ NEEDS WORK: Many sections are missing data")
                
        else:
            print(f"❌ Prediction failed: {pred_response.status_code}")
            print(f"   Error: {pred_response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend connection failed: {e}")
    
    # Test frontend
    try:
        frontend_response = requests.get("http://localhost:5173/", timeout=5)
        print(f"\n✅ Frontend Status: {frontend_response.status_code}")
        print("✅ Web interface is accessible at http://localhost:5173")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Frontend connection failed: {e}")
    
    print(f"\n🎉 System Test Complete!")
    print("📝 Next Steps:")
    print("   1. Open http://localhost:5173 in your browser")
    print("   2. Upload any image file")
    print("   3. Click 'Analyze' to see comprehensive plant information")
    print("   4. Check all tabs: Overview, Traditional Use, Preparation, Safety, Geography")

if __name__ == "__main__":
    test_complete_system()
