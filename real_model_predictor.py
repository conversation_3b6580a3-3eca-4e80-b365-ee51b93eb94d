#!/usr/bin/env python3
"""
Real Plant Model Predictor
Integrates trained PyTorch model for actual plant identification
"""

import torch
import torch.nn as nn
from torchvision import transforms, models
from PIL import Image
import json
import numpy as np
from pathlib import Path
import cv2

class PlantClassifier(nn.Module):
    """Same architecture as training script"""
    def __init__(self, num_classes, pretrained=True):
        super(PlantClassifier, self).__init__()
        
        # Use EfficientNet as backbone
        self.backbone = models.efficientnet_b0(pretrained=pretrained)
        
        # Replace classifier
        num_features = self.backbone.classifier[1].in_features
        self.backbone.classifier = nn.Sequential(
            nn.Dropout(0.3),
            nn.Linear(num_features, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, num_classes)
        )
    
    def forward(self, x):
        return self.backbone(x)

class RealPlantPredictor:
    def __init__(self, model_path="best_plant_model.pth", classes_file="Medicinal-Plant-Backend/models/classes.json"):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.classes_file = classes_file
        
        # Load plant classes data
        with open(self.classes_file, 'r') as f:
            self.plant_classes = json.load(f)
        
        # Load model
        self.model = None
        self.class_names = []
        self.model_loaded = False
        
        if Path(model_path).exists():
            self.load_model(model_path)
        else:
            print(f"⚠️ Model file not found: {model_path}")
            print("Using mock prediction system")
        
        # Image preprocessing
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    def load_model(self, model_path):
        """Load the trained model"""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # Get class names from checkpoint
            self.class_names = checkpoint.get('class_names', [])
            num_classes = len(self.class_names)
            
            # Create and load model
            self.model = PlantClassifier(num_classes, pretrained=False)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model = self.model.to(self.device)
            self.model.eval()
            
            self.model_loaded = True
            print(f"✅ Model loaded successfully")
            print(f"📊 Classes: {num_classes}")
            print(f"🎯 Best validation accuracy: {checkpoint.get('val_acc', 'Unknown'):.2f}%")
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            self.model_loaded = False
    
    def preprocess_image(self, image_data):
        """Preprocess image for model prediction"""
        try:
            # Convert bytes to PIL Image
            if isinstance(image_data, bytes):
                image = Image.open(io.BytesIO(image_data)).convert('RGB')
            elif isinstance(image_data, str):
                image = Image.open(image_data).convert('RGB')
            elif isinstance(image_data, Image.Image):
                image = image.convert('RGB')
            else:
                raise ValueError("Unsupported image format")
            
            # Apply transforms
            image_tensor = self.transform(image).unsqueeze(0)
            return image_tensor.to(self.device)
            
        except Exception as e:
            print(f"❌ Image preprocessing failed: {e}")
            return None
    
    def predict_plant(self, image_data):
        """Predict plant species from image"""
        if not self.model_loaded:
            return self.mock_prediction()
        
        try:
            # Preprocess image
            image_tensor = self.preprocess_image(image_data)
            if image_tensor is None:
                return self.mock_prediction()
            
            # Make prediction
            with torch.no_grad():
                outputs = self.model(image_tensor)
                probabilities = torch.nn.functional.softmax(outputs[0], dim=0)
                confidence, predicted_idx = torch.max(probabilities, 0)
                
                confidence = confidence.item()
                predicted_idx = predicted_idx.item()
            
            # Get predicted class name
            if predicted_idx < len(self.class_names):
                predicted_class = self.class_names[predicted_idx]
                
                # Find corresponding plant data
                plant_data = self.find_plant_by_class_name(predicted_class)
                if plant_data:
                    return self.format_prediction_result(plant_data, confidence)
            
            # Fallback to mock if something goes wrong
            return self.mock_prediction()
            
        except Exception as e:
            print(f"❌ Prediction failed: {e}")
            return self.mock_prediction()
    
    def find_plant_by_class_name(self, class_name):
        """Find plant data by class name"""
        # Class name format: scientific_name.lower().replace(' ', '_')
        scientific_name = class_name.replace('_', ' ').title()
        
        for plant_id, plant_data in self.plant_classes.items():
            if plant_data['scientific_name'].lower() == scientific_name.lower():
                return plant_data
        
        # If not found by scientific name, try by local name
        for plant_id, plant_data in self.plant_classes.items():
            if plant_data['local_name'].lower().replace(' ', '_') == class_name.lower():
                return plant_data
        
        return None
    
    def format_prediction_result(self, plant_data, confidence):
        """Format prediction result to match API response"""
        return {
            "scientificName": plant_data['scientific_name'],
            "localName": plant_data['local_name'],
            "realName": plant_data.get('real_name', plant_data['local_name']),
            "commonNames": plant_data.get('common_names', []),
            "primaryMedicine": plant_data.get('primary_medicine', 'Traditional medicinal use'),
            "confidence": confidence,
            "description": plant_data.get('description', {}),
            "medicinalFeatures": plant_data.get('medicinal_features', {}),
            "traditionalSystems": plant_data.get('traditional_systems', {}),
            "preparationMethods": plant_data.get('preparation_methods', {}),
            "safetyInfo": plant_data.get('safety_info', {}),
            "geographicalInfo": plant_data.get('geographical_info', {}),
            "modelUsed": "real_trained_model"
        }
    
    def mock_prediction(self):
        """Fallback mock prediction when real model is not available"""
        import random
        
        # Select a random plant with comprehensive data
        comprehensive_plants = []
        basic_plants = []
        
        for plant_id, plant_data in self.plant_classes.items():
            has_traditional = bool(plant_data.get('traditional_systems'))
            has_preparation = bool(plant_data.get('preparation_methods'))
            has_safety = bool(plant_data.get('safety_info'))
            
            if has_traditional and has_preparation and has_safety:
                comprehensive_plants.append(plant_id)
            else:
                basic_plants.append(plant_id)
        
        # 70% chance to select from comprehensive plants
        if comprehensive_plants and random.random() < 0.7:
            selected_id = random.choice(comprehensive_plants)
        else:
            selected_id = random.choice(basic_plants) if basic_plants else random.choice(list(self.plant_classes.keys()))
        
        plant_data = self.plant_classes[selected_id]
        mock_confidence = random.uniform(0.4, 0.9)
        
        result = self.format_prediction_result(plant_data, mock_confidence)
        result["modelUsed"] = "mock_system"
        return result
    
    def get_top_k_predictions(self, image_data, k=3):
        """Get top-k predictions with confidence scores"""
        if not self.model_loaded:
            return [self.mock_prediction()]
        
        try:
            # Preprocess image
            image_tensor = self.preprocess_image(image_data)
            if image_tensor is None:
                return [self.mock_prediction()]
            
            # Make prediction
            with torch.no_grad():
                outputs = self.model(image_tensor)
                probabilities = torch.nn.functional.softmax(outputs[0], dim=0)
                
                # Get top-k predictions
                top_k_probs, top_k_indices = torch.topk(probabilities, k)
            
            results = []
            for i in range(k):
                confidence = top_k_probs[i].item()
                predicted_idx = top_k_indices[i].item()
                
                if predicted_idx < len(self.class_names):
                    predicted_class = self.class_names[predicted_idx]
                    plant_data = self.find_plant_by_class_name(predicted_class)
                    
                    if plant_data:
                        result = self.format_prediction_result(plant_data, confidence)
                        results.append(result)
            
            return results if results else [self.mock_prediction()]
            
        except Exception as e:
            print(f"❌ Top-k prediction failed: {e}")
            return [self.mock_prediction()]
    
    def get_model_info(self):
        """Get information about the loaded model"""
        return {
            "model_loaded": self.model_loaded,
            "num_classes": len(self.class_names) if self.model_loaded else 0,
            "device": str(self.device),
            "class_names": self.class_names[:10] if self.model_loaded else [],  # First 10 for brevity
            "total_plants_in_db": len(self.plant_classes)
        }

# Global predictor instance
predictor = None

def get_predictor():
    """Get or create the global predictor instance"""
    global predictor
    if predictor is None:
        predictor = RealPlantPredictor()
    return predictor

def predict_plant_species(image_data):
    """Main prediction function for integration with Flask app"""
    predictor = get_predictor()
    return predictor.predict_plant(image_data)

def get_top_predictions(image_data, k=3):
    """Get top-k predictions"""
    predictor = get_predictor()
    return predictor.get_top_k_predictions(image_data, k)

def test_predictor():
    """Test the predictor with a sample image"""
    print("🧪 Testing Plant Predictor")
    print("=" * 30)
    
    predictor = get_predictor()
    info = predictor.get_model_info()
    
    print(f"Model loaded: {info['model_loaded']}")
    print(f"Device: {info['device']}")
    print(f"Classes: {info['num_classes']}")
    print(f"Plants in DB: {info['total_plants_in_db']}")
    
    # Test with mock data
    mock_result = predictor.mock_prediction()
    print(f"\nSample prediction:")
    print(f"Plant: {mock_result['scientificName']}")
    print(f"Confidence: {mock_result['confidence']:.1%}")
    print(f"Model: {mock_result['modelUsed']}")

if __name__ == "__main__":
    test_predictor()
