"""ingest_frontend_csv.py
Read the frontend `public/uploads/plant-features.csv`, pick a small subset of classes,
download one image per class (or create a placeholder), and populate
`data/sample/train/{class}` and `data/sample/val/{class}` with images.
Also write `labels.csv` (class_name,scientificName,localName,medicinalFeature)
and run `make_classes.py` to produce `models/classes.json`.

Usage:
  python ingest_frontend_csv.py --csv ../Medicinal-Plant-Web/public/uploads/plant-features.csv --out data/sample --n 20

This script is safe to run on Windows without pandas.
"""
import csv
import os
import shutil
import argparse
from pathlib import Path
import logging
import sys
import re
import base64

SAMPLE_PER_CLASS = 1

def ensure_dir(p):
    os.makedirs(p, exist_ok=True)

def download(url, outpath):
    try:
        import requests
        response = requests.get(url, timeout=20)
        response.raise_for_status()  # Raises an HTTPError for bad responses (4xx or 5xx)
        with open(outpath, 'wb') as fh:
            fh.write(response.content)
        return True
    except ImportError:
        logging.error("The 'requests' library is not installed. Please run: pip install requests")
    except requests.exceptions.RequestException as e:
        logging.warning(f"Failed to download {url}: {e}")
    return False

def create_placeholder(outpath, text='placeholder'):
    outpath = str(outpath)
    try:
        from PIL import Image, ImageDraw, ImageFont
        img = Image.new('RGB', (224, 224), color=(200, 220, 255))
        d = ImageDraw.Draw(img)
        try:
            f = ImageFont.load_default()
            d.text((10, 100), text, fill=(10, 10, 10), font=f)
        except Exception:
            # Fallback if font loading fails
            d.text((10, 100), text, fill=(10, 10, 10))
        img.save(outpath, format='JPEG')
    except ImportError:
        logging.error("The 'Pillow' library is not installed. Please run: pip install Pillow")
        # fallback: write a tiny 1x1 PNG (base64) so there's a valid image file
        png_b64 = b'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGNgYAAAAAMAASsJTYQAAAAASUVORK5CYII='
        try:
            with open(outpath, 'wb') as fh:
                fh.write(base64.b64decode(png_b64))
        except Exception as e:
            logging.error(f'Failed to write placeholder to {outpath}: {e}')

def read_csv_rows(csv_path):
    """Reads rows from a CSV file, skipping fully empty ones."""
    rows = []
    with open(csv_path, newline='', encoding='utf-8') as fh:
        rdr = csv.DictReader(fh)
        for r in rdr:
            # skip empty rows
            if not (r.get('scientificName') or r.get('localName') or r.get('features') or r.get('photo')):
                continue
            rows.append(r)
    return rows

def main():
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

    p = argparse.ArgumentParser()
    p.add_argument('--csv', default=os.path.join('..', 'Medicinal-Plant-Web', 'public', 'uploads', 'plant-features.csv'))
    p.add_argument('--out', default='data/sample')
    p.add_argument('--n', type=int, default=30, help='number of classes to ingest (top rows)')
    args = p.parse_args()

    csv_path = Path(args.csv).resolve()
    out_base = Path(args.out)
    project_root = Path(__file__).parent

    if not csv_path.exists():
        logging.error(f'CSV not found: {csv_path}')
        return

    rows = read_csv_rows(csv_path)
    if not rows:
        logging.error('No rows found in CSV')
        return

    # choose first n unique scientificName/localName combos
    selected = []
    seen = set()
    for r in rows:
        key = (r.get('scientificName') or '').strip()
        if not key:
            continue
        if key in seen:
            continue
        seen.add(key)
        selected.append(r)
        if len(selected) >= args.n:
            break

    if not selected:
        logging.error('No valid classes selected')
        return

    logging.info(f'Selected {len(selected)} classes to ingest')

    # clear and create train/val dirs
    train_dir = out_base / 'train'
    val_dir = out_base / 'val'
    if out_base.exists():
        logging.info(f'Removing existing directory: {out_base}')
        shutil.rmtree(out_base)
    for d in [train_dir, val_dir]:
        ensure_dir(d)

    labels_out = project_root / 'labels.csv'
    # write labels.csv header
    with open(labels_out, 'w', newline='', encoding='utf-8') as fh:
        writer = csv.writer(fh)
        writer.writerow(['class_name', 'scientificName', 'localName', 'medicinalFeature'])

        for i, r in enumerate(selected):
            # derive a safe class name from scientificName or localName
            name = (r.get('scientificName') or r.get('localName') or f'class_{i}').strip()
            # Sanitize to create a valid directory/file name
            class_name = re.sub(r'[^a-z0-9_]+', '', name.lower().replace(' ', '_'))
            # write CSV line
            writer.writerow([class_name, r.get('scientificName', ''), r.get('localName', ''), r.get('features', '')])

            # prepare train/val dirs for this class
            cls_train = train_dir / class_name
            cls_val = val_dir / class_name
            ensure_dir(cls_train)
            ensure_dir(cls_val)

            photo = (r.get('photo') or '').strip()
            outpath = cls_train / f'{class_name}_0.jpg'
            ok = False
            if photo:
                ok = download(photo, outpath)
                if not ok:
                    logging.warning(f'Download failed for {class_name}, creating placeholder.')
            if not ok:
                create_placeholder(outpath, text=class_name)

            # copy one file to validation
            dst_val = cls_val / f'{class_name}_0.jpg'
            try:
                shutil.copy(outpath, dst_val)
            except Exception as e:
                logging.error(f'Failed to copy to val for {class_name}: {e}')
            logging.info(f'Processed class: {class_name}')

    logging.info(f'Wrote labels.csv to {labels_out}')

    logging.info(f'Sample dataset prepared at {out_base}')

if __name__ == '__main__':
    main()
