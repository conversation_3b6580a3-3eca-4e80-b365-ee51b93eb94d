import os
import requests
from bs4 import BeautifulSoup
from pathlib import Path
import time

def scrape_plant_images(plant_name, output_dir='dataset/raw', num_images=10):
    """
    Scrape images for a given plant name from a search engine or botanical site.
    This is a basic example using Google Images (but respect terms).
    In practice, use a proper API or manual collection.
    """
    # For demo, use a placeholder. Real scraping needs care for terms of service.
    # Example: Search on PlantNet or iNaturalist API.

    # Placeholder: Assume we have a list of URLs or use requests to download known images.
    # For now, print and skip actual download.

    query = f"{plant_name} medicinal plant"
    url = f"https://www.google.com/search?q={query}&tbm=isch"

    headers = {'User-Agent': 'Mozilla/5.0'}
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, 'html.parser')

    img_tags = soup.find_all('img', limit=num_images + 1)  # Skip first logo

    output_path = Path(output_dir) / plant_name.replace(' ', '_')
    output_path.mkdir(parents=True, exist_ok=True)

    for i, img in enumerate(img_tags[1:], 1):  # Skip first
        img_url = img.get('src')
        if img_url and img_url.startswith('http'):
            try:
                img_response = requests.get(img_url, headers=headers, timeout=5)
                if img_response.status_code == 200:
                    img_path = output_path / f"{i}.jpg"
                    with open(img_path, 'wb') as f:
                        f.write(img_response.content)
                    print(f"Downloaded {img_path}")
                    time.sleep(1)  # Respectful delay
            except Exception as e:
                print(f"Failed to download {img_url}: {e}")
        if i >= num_images:
            break

if __name__ == '__main__':
    from labels_csv import load_metadata  # Assuming we have a function
    meta = load_metadata('labels.csv')
    for class_name in meta.keys()[:5]:  # Limit to first 5 for demo
        scrape_plant_images(class_name)