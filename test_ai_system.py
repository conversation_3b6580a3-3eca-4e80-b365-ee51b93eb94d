#!/usr/bin/env python3
"""
Test Script for AI Plant Recognition System
Tests the complete pipeline from image generation to model prediction
"""

import os
import json
import requests
import time
from pathlib import Path
import subprocess
import sys

class AISystemTester:
    def __init__(self):
        self.base_url = "http://localhost:5000"
        self.test_results = {}
        
    def test_dependencies(self):
        """Test if all required packages are installed"""
        print("🔍 Testing Dependencies")
        print("=" * 25)
        
        required_packages = [
            "torch", "torchvision", "transformers", "diffusers",
            "PIL", "cv2", "albumentations", "numpy", "requests"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                if package == "PIL":
                    import PIL
                elif package == "cv2":
                    import cv2
                else:
                    __import__(package)
                print(f"✅ {package}")
            except ImportError:
                print(f"❌ {package} - Missing")
                missing_packages.append(package)
        
        self.test_results["dependencies"] = len(missing_packages) == 0
        
        if missing_packages:
            print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
            print("Run: pip install torch torchvision transformers diffusers pillow opencv-python albumentations")
        
        return len(missing_packages) == 0
    
    def test_model_files(self):
        """Test if model files exist"""
        print("\n📁 Testing Model Files")
        print("=" * 25)
        
        files_to_check = [
            "best_plant_model.pth",
            "real_model_predictor.py",
            "ai_image_generator.py",
            "image_augmentation.py",
            "train_plant_model.py"
        ]
        
        missing_files = []
        for file_path in files_to_check:
            if Path(file_path).exists():
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path} - Missing")
                missing_files.append(file_path)
        
        self.test_results["model_files"] = len(missing_files) == 0
        return len(missing_files) == 0
    
    def test_real_model_predictor(self):
        """Test the real model predictor"""
        print("\n🤖 Testing Real Model Predictor")
        print("=" * 35)
        
        try:
            # Import and test the predictor
            sys.path.append('.')
            from real_model_predictor import RealPlantPredictor, test_predictor
            
            # Run the test function
            test_predictor()
            
            print("✅ Real model predictor working")
            self.test_results["real_predictor"] = True
            return True
            
        except Exception as e:
            print(f"❌ Real model predictor failed: {e}")
            self.test_results["real_predictor"] = False
            return False
    
    def test_backend_integration(self):
        """Test Flask backend integration"""
        print("\n🌐 Testing Backend Integration")
        print("=" * 35)
        
        try:
            # Test basic connectivity
            response = requests.get(f"{self.base_url}/", timeout=5)
            if response.status_code == 200:
                print("✅ Backend server accessible")
            else:
                print(f"❌ Backend server error: {response.status_code}")
                return False
            
            # Test model info endpoint
            response = requests.get(f"{self.base_url}/api/model-info", timeout=5)
            if response.status_code == 200:
                model_info = response.json()
                print(f"✅ Model info: {model_info.get('status', 'unknown')}")
                print(f"   Classes: {model_info.get('num_classes', 0)}")
                print(f"   Device: {model_info.get('device', 'unknown')}")
            else:
                print(f"❌ Model info endpoint failed: {response.status_code}")
            
            # Test prediction endpoint with dummy image
            test_image_data = self.create_test_image()
            files = {'image': ('test.jpg', test_image_data, 'image/jpeg')}
            
            response = requests.post(f"{self.base_url}/api/predict", files=files, timeout=10)
            if response.status_code == 200:
                prediction = response.json()
                print(f"✅ Prediction API working")
                print(f"   Plant: {prediction.get('scientificName', 'Unknown')}")
                print(f"   Confidence: {prediction.get('confidence', 0):.1%}")
                print(f"   Model: {prediction.get('modelUsed', 'unknown')}")
            else:
                print(f"❌ Prediction API failed: {response.status_code}")
                return False
            
            self.test_results["backend"] = True
            return True
            
        except requests.exceptions.ConnectionError:
            print("❌ Backend server not running")
            print("   Start with: python Medicinal-Plant-Backend/app.py")
            self.test_results["backend"] = False
            return False
        except Exception as e:
            print(f"❌ Backend test failed: {e}")
            self.test_results["backend"] = False
            return False
    
    def create_test_image(self):
        """Create a minimal test image"""
        # Minimal JPEG header for testing
        jpeg_data = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
        return jpeg_data
    
    def test_dataset_structure(self):
        """Test dataset structure if it exists"""
        print("\n📊 Testing Dataset Structure")
        print("=" * 32)
        
        dataset_dirs = [
            "generated_dataset",
            "augmented_dataset", 
            "training_dataset"
        ]
        
        for dataset_dir in dataset_dirs:
            if Path(dataset_dir).exists():
                plant_dirs = [d for d in Path(dataset_dir).iterdir() if d.is_dir()]
                total_images = 0
                for plant_dir in plant_dirs:
                    images = list(plant_dir.glob("*.jpg")) + list(plant_dir.glob("*.png"))
                    total_images += len(images)
                
                print(f"✅ {dataset_dir}: {len(plant_dirs)} classes, {total_images} images")
            else:
                print(f"⚠️ {dataset_dir}: Not found")
        
        # Check training dataset splits
        training_dir = Path("training_dataset")
        if training_dir.exists():
            for split in ["train", "val", "test"]:
                split_dir = training_dir / split
                if split_dir.exists():
                    classes = [d for d in split_dir.iterdir() if d.is_dir()]
                    total_images = sum(len(list(class_dir.glob("*.jpg")) + list(class_dir.glob("*.png"))) 
                                     for class_dir in classes)
                    print(f"   {split}: {len(classes)} classes, {total_images} images")
        
        self.test_results["dataset"] = True
        return True
    
    def test_training_artifacts(self):
        """Test training artifacts"""
        print("\n🏆 Testing Training Artifacts")
        print("=" * 35)
        
        artifacts = [
            ("best_plant_model.pth", "Trained model"),
            ("training_history.json", "Training history"),
            ("evaluation_results.txt", "Evaluation results"),
            ("dataset_metadata.json", "Dataset metadata")
        ]
        
        found_artifacts = 0
        for artifact_file, description in artifacts:
            if Path(artifact_file).exists():
                print(f"✅ {description}: {artifact_file}")
                found_artifacts += 1
            else:
                print(f"⚠️ {description}: {artifact_file} - Not found")
        
        self.test_results["artifacts"] = found_artifacts > 0
        return found_artifacts > 0
    
    def run_comprehensive_test(self):
        """Run all tests"""
        print("🧪 AI Plant Recognition System - Comprehensive Test")
        print("=" * 60)
        
        # Run all tests
        tests = [
            ("Dependencies", self.test_dependencies),
            ("Model Files", self.test_model_files),
            ("Real Predictor", self.test_real_model_predictor),
            ("Backend Integration", self.test_backend_integration),
            ("Dataset Structure", self.test_dataset_structure),
            ("Training Artifacts", self.test_training_artifacts)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed_tests += 1
            except Exception as e:
                print(f"❌ {test_name} test failed with error: {e}")
        
        # Summary
        print("\n" + "=" * 60)
        print("🎯 Test Summary")
        print("=" * 15)
        print(f"Tests passed: {passed_tests}/{total_tests}")
        print(f"Success rate: {passed_tests/total_tests*100:.1f}%")
        
        if passed_tests == total_tests:
            print("🎉 All tests passed! Your AI system is fully functional.")
        elif passed_tests >= total_tests * 0.7:
            print("✅ Most tests passed. System is mostly functional.")
        else:
            print("⚠️ Several tests failed. System needs attention.")
        
        # Recommendations
        print("\n📋 Recommendations:")
        if not self.test_results.get("dependencies", False):
            print("- Install missing dependencies")
        if not self.test_results.get("model_files", False):
            print("- Run setup script to create model files")
        if not self.test_results.get("backend", False):
            print("- Start the Flask backend server")
        if not self.test_results.get("artifacts", False):
            print("- Train the model to generate artifacts")
        
        return passed_tests / total_tests

def main():
    print("🔬 Starting AI System Tests...")
    
    tester = AISystemTester()
    success_rate = tester.run_comprehensive_test()
    
    if success_rate >= 0.8:
        print("\n🚀 System ready for use!")
        print("Run: python start_project.py")
    else:
        print("\n🔧 System needs setup/fixes")
        print("Run: python setup_ai_dataset.py")

if __name__ == "__main__":
    main()
