{"version": 3, "names": ["_t", "require", "_index", "isClassDeclaration", "isExportDefaultSpecifier", "isExportNamespaceSpecifier", "isImportDefaultSpecifier", "isImportNamespaceSpecifier", "isStatement", "ImportSpecifier", "node", "importKind", "word", "space", "print", "imported", "local", "name", "ImportDefaultSpecifier", "ExportDefaultSpecifier", "exported", "ExportSpecifier", "exportKind", "ExportNamespaceSpecifier", "token", "warningShown", "_printAttributes", "hasPreviousBrace", "_node$extra", "importAttributesKeyword", "format", "attributes", "assertions", "extra", "deprecatedAssertSyntax", "deprecatedWithLegacySyntax", "console", "warn", "useAssertKeyword", "printList", "occurrenceCount", "shouldPrintTrailingComma", "ExportAllDeclaration", "_node$attributes", "_node$assertions", "length", "source", "semicolon", "maybePrintDecoratorsBeforeExport", "printer", "declaration", "_shouldPrintDecoratorsBeforeExport", "printJoin", "decorators", "ExportNamedDeclaration", "declar", "specifiers", "slice", "hasSpecial", "first", "shift", "hasBrace", "_node$attributes2", "_node$assertions2", "ExportDefaultDeclaration", "noIndentInnerCommentsHere", "tokenContext", "TokenContext", "exportDefault", "ImportDeclaration", "_node$attributes3", "_node$assertions3", "isTypeKind", "module", "phase", "hasSpecifiers", "ImportAttribute", "key", "value", "ImportNamespaceSpecifier", "ImportExpression", "options", "rightParens"], "sources": ["../../src/generators/modules.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport {\n  isClassDeclaration,\n  isExportDefaultSpecifier,\n  isExportNamespaceSpecifier,\n  isImportDefaultSpecifier,\n  isImportNamespaceSpecifier,\n  isStatement,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { TokenContext } from \"../node/index.ts\";\n\nexport function ImportSpecifier(this: Printer, node: t.ImportSpecifier) {\n  if (node.importKind === \"type\" || node.importKind === \"typeof\") {\n    this.word(node.importKind);\n    this.space();\n  }\n\n  this.print(node.imported);\n  // @ts-expect-error todo(flow-ts) maybe check node type instead of relying on name to be undefined on t.StringLiteral\n  if (node.local && node.local.name !== node.imported.name) {\n    this.space();\n    this.word(\"as\");\n    this.space();\n    this.print(node.local);\n  }\n}\n\nexport function ImportDefaultSpecifier(\n  this: Printer,\n  node: t.ImportDefaultSpecifier,\n) {\n  this.print(node.local);\n}\n\nexport function ExportDefaultSpecifier(\n  this: Printer,\n  node: t.ExportDefaultSpecifier,\n) {\n  this.print(node.exported);\n}\n\nexport function ExportSpecifier(this: Printer, node: t.ExportSpecifier) {\n  if (node.exportKind === \"type\") {\n    this.word(\"type\");\n    this.space();\n  }\n\n  this.print(node.local);\n  // @ts-expect-error todo(flow-ts) maybe check node type instead of relying on name to be undefined on t.StringLiteral\n  if (node.exported && node.local.name !== node.exported.name) {\n    this.space();\n    this.word(\"as\");\n    this.space();\n    this.print(node.exported);\n  }\n}\n\nexport function ExportNamespaceSpecifier(\n  this: Printer,\n  node: t.ExportNamespaceSpecifier,\n) {\n  this.token(\"*\");\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.print(node.exported);\n}\n\nlet warningShown = false;\n\nexport function _printAttributes(\n  this: Printer,\n  node: Extract<t.Node, { attributes?: t.ImportAttribute[] }>,\n  hasPreviousBrace: boolean,\n) {\n  const { importAttributesKeyword } = this.format;\n  const { attributes, assertions } = node;\n\n  if (\n    !process.env.BABEL_8_BREAKING &&\n    attributes &&\n    !importAttributesKeyword &&\n    node.extra &&\n    (node.extra.deprecatedAssertSyntax ||\n      node.extra.deprecatedWithLegacySyntax) &&\n    // In the production build only show the warning once.\n    // We want to show it per-usage locally for tests.\n    (!process.env.IS_PUBLISH || !warningShown)\n  ) {\n    warningShown = true;\n    console.warn(`\\\nYou are using import attributes, without specifying the desired output syntax.\nPlease specify the \"importAttributesKeyword\" generator option, whose value can be one of:\n - \"with\"        : \\`import { a } from \"b\" with { type: \"json\" };\\`\n - \"assert\"      : \\`import { a } from \"b\" assert { type: \"json\" };\\`\n - \"with-legacy\" : \\`import { a } from \"b\" with type: \"json\";\\`\n`);\n  }\n\n  const useAssertKeyword =\n    importAttributesKeyword === \"assert\" ||\n    (!importAttributesKeyword && assertions);\n\n  this.word(useAssertKeyword ? \"assert\" : \"with\");\n  this.space();\n\n  if (\n    !process.env.BABEL_8_BREAKING &&\n    !useAssertKeyword &&\n    (importAttributesKeyword === \"with-legacy\" ||\n      (!importAttributesKeyword && node.extra?.deprecatedWithLegacySyntax))\n  ) {\n    // with-legacy\n    this.printList(attributes || assertions);\n    return;\n  }\n\n  const occurrenceCount = hasPreviousBrace ? 1 : 0;\n\n  this.token(\"{\", null, occurrenceCount);\n  this.space();\n  this.printList(attributes || assertions, this.shouldPrintTrailingComma(\"}\"));\n  this.space();\n  this.token(\"}\", null, occurrenceCount);\n}\n\nexport function ExportAllDeclaration(\n  this: Printer,\n  node: t.ExportAllDeclaration | t.DeclareExportAllDeclaration,\n) {\n  this.word(\"export\");\n  this.space();\n  if (node.exportKind === \"type\") {\n    this.word(\"type\");\n    this.space();\n  }\n  this.token(\"*\");\n  this.space();\n  this.word(\"from\");\n  this.space();\n  if (node.attributes?.length || node.assertions?.length) {\n    this.print(node.source, true);\n    this.space();\n    this._printAttributes(node, false);\n  } else {\n    this.print(node.source);\n  }\n\n  this.semicolon();\n}\n\nfunction maybePrintDecoratorsBeforeExport(\n  printer: Printer,\n  node: t.ExportNamedDeclaration | t.ExportDefaultDeclaration,\n) {\n  if (\n    isClassDeclaration(node.declaration) &&\n    printer._shouldPrintDecoratorsBeforeExport(\n      node as t.ExportNamedDeclaration & { declaration: t.ClassDeclaration },\n    )\n  ) {\n    printer.printJoin(node.declaration.decorators);\n  }\n}\n\nexport function ExportNamedDeclaration(\n  this: Printer,\n  node: t.ExportNamedDeclaration,\n) {\n  maybePrintDecoratorsBeforeExport(this, node);\n\n  this.word(\"export\");\n  this.space();\n  if (node.declaration) {\n    const declar = node.declaration;\n    this.print(declar);\n    if (!isStatement(declar)) this.semicolon();\n  } else {\n    if (node.exportKind === \"type\") {\n      this.word(\"type\");\n      this.space();\n    }\n\n    const specifiers = node.specifiers.slice(0);\n\n    // print \"special\" specifiers first\n    let hasSpecial = false;\n    for (;;) {\n      const first = specifiers[0];\n      if (\n        isExportDefaultSpecifier(first) ||\n        isExportNamespaceSpecifier(first)\n      ) {\n        hasSpecial = true;\n        this.print(specifiers.shift());\n        if (specifiers.length) {\n          this.token(\",\");\n          this.space();\n        }\n      } else {\n        break;\n      }\n    }\n\n    let hasBrace = false;\n    if (specifiers.length || (!specifiers.length && !hasSpecial)) {\n      hasBrace = true;\n      this.token(\"{\");\n      if (specifiers.length) {\n        this.space();\n        this.printList(specifiers, this.shouldPrintTrailingComma(\"}\"));\n        this.space();\n      }\n      this.token(\"}\");\n    }\n\n    if (node.source) {\n      this.space();\n      this.word(\"from\");\n      this.space();\n      if (node.attributes?.length || node.assertions?.length) {\n        this.print(node.source, true);\n        this.space();\n        this._printAttributes(node, hasBrace);\n      } else {\n        this.print(node.source);\n      }\n    }\n\n    this.semicolon();\n  }\n}\n\nexport function ExportDefaultDeclaration(\n  this: Printer,\n  node: t.ExportDefaultDeclaration,\n) {\n  maybePrintDecoratorsBeforeExport(this, node);\n\n  this.word(\"export\");\n  this.noIndentInnerCommentsHere();\n  this.space();\n  this.word(\"default\");\n  this.space();\n  this.tokenContext |= TokenContext.exportDefault;\n  const declar = node.declaration;\n  this.print(declar);\n  if (!isStatement(declar)) this.semicolon();\n}\n\nexport function ImportDeclaration(this: Printer, node: t.ImportDeclaration) {\n  this.word(\"import\");\n  this.space();\n\n  const isTypeKind = node.importKind === \"type\" || node.importKind === \"typeof\";\n  if (isTypeKind) {\n    this.noIndentInnerCommentsHere();\n    this.word(node.importKind);\n    this.space();\n  } else if (node.module) {\n    this.noIndentInnerCommentsHere();\n    this.word(\"module\");\n    this.space();\n  } else if (node.phase) {\n    this.noIndentInnerCommentsHere();\n    this.word(node.phase);\n    this.space();\n  }\n\n  const specifiers = node.specifiers.slice(0);\n  const hasSpecifiers = !!specifiers.length;\n  // print \"special\" specifiers first. The loop condition is constant,\n  // but there is a \"break\" in the body.\n  while (hasSpecifiers) {\n    const first = specifiers[0];\n    if (isImportDefaultSpecifier(first) || isImportNamespaceSpecifier(first)) {\n      this.print(specifiers.shift());\n      if (specifiers.length) {\n        this.token(\",\");\n        this.space();\n      }\n    } else {\n      break;\n    }\n  }\n\n  let hasBrace = false;\n  if (specifiers.length) {\n    hasBrace = true;\n    this.token(\"{\");\n    this.space();\n    this.printList(specifiers, this.shouldPrintTrailingComma(\"}\"));\n    this.space();\n    this.token(\"}\");\n  } else if (isTypeKind && !hasSpecifiers) {\n    hasBrace = true;\n    this.token(\"{\");\n    this.token(\"}\");\n  }\n\n  if (hasSpecifiers || isTypeKind) {\n    this.space();\n    this.word(\"from\");\n    this.space();\n  }\n\n  if (node.attributes?.length || node.assertions?.length) {\n    this.print(node.source, true);\n    this.space();\n    this._printAttributes(node, hasBrace);\n  } else {\n    this.print(node.source);\n  }\n\n  this.semicolon();\n}\n\nexport function ImportAttribute(this: Printer, node: t.ImportAttribute) {\n  this.print(node.key);\n  this.token(\":\");\n  this.space();\n  this.print(node.value);\n}\n\nexport function ImportNamespaceSpecifier(\n  this: Printer,\n  node: t.ImportNamespaceSpecifier,\n) {\n  this.token(\"*\");\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.print(node.local);\n}\n\nexport function ImportExpression(this: Printer, node: t.ImportExpression) {\n  this.word(\"import\");\n  if (node.phase) {\n    this.token(\".\");\n    this.word(node.phase);\n  }\n  this.token(\"(\");\n  const shouldPrintTrailingComma = this.shouldPrintTrailingComma(\")\");\n  this.print(node.source);\n  if (node.options != null) {\n    this.token(\",\");\n    this.space();\n    this.print(node.options);\n  }\n  if (shouldPrintTrailingComma) {\n    this.token(\",\");\n  }\n  this.rightParens(node);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AASA,IAAAC,MAAA,GAAAD,OAAA;AAAgD;EAR9CE,kBAAkB;EAClBC,wBAAwB;EACxBC,0BAA0B;EAC1BC,wBAAwB;EACxBC,0BAA0B;EAC1BC;AAAW,IAAAR,EAAA;AAKN,SAASS,eAAeA,CAAgBC,IAAuB,EAAE;EACtE,IAAIA,IAAI,CAACC,UAAU,KAAK,MAAM,IAAID,IAAI,CAACC,UAAU,KAAK,QAAQ,EAAE;IAC9D,IAAI,CAACC,IAAI,CAACF,IAAI,CAACC,UAAU,CAAC;IAC1B,IAAI,CAACE,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACK,QAAQ,CAAC;EAEzB,IAAIL,IAAI,CAACM,KAAK,IAAIN,IAAI,CAACM,KAAK,CAACC,IAAI,KAAKP,IAAI,CAACK,QAAQ,CAACE,IAAI,EAAE;IACxD,IAAI,CAACJ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACM,KAAK,CAAC;EACxB;AACF;AAEO,SAASE,sBAAsBA,CAEpCR,IAA8B,EAC9B;EACA,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACM,KAAK,CAAC;AACxB;AAEO,SAASG,sBAAsBA,CAEpCT,IAA8B,EAC9B;EACA,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACU,QAAQ,CAAC;AAC3B;AAEO,SAASC,eAAeA,CAAgBX,IAAuB,EAAE;EACtE,IAAIA,IAAI,CAACY,UAAU,KAAK,MAAM,EAAE;IAC9B,IAAI,CAACV,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACM,KAAK,CAAC;EAEtB,IAAIN,IAAI,CAACU,QAAQ,IAAIV,IAAI,CAACM,KAAK,CAACC,IAAI,KAAKP,IAAI,CAACU,QAAQ,CAACH,IAAI,EAAE;IAC3D,IAAI,CAACJ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACU,QAAQ,CAAC;EAC3B;AACF;AAEO,SAASG,wBAAwBA,CAEtCb,IAAgC,EAChC;EACA,IAAI,CAACc,SAAK,GAAI,CAAC;EACf,IAAI,CAACX,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACU,QAAQ,CAAC;AAC3B;AAEA,IAAIK,YAAY,GAAG,KAAK;AAEjB,SAASC,gBAAgBA,CAE9BhB,IAA2D,EAC3DiB,gBAAyB,EACzB;EAAA,IAAAC,WAAA;EACA,MAAM;IAAEC;EAAwB,CAAC,GAAG,IAAI,CAACC,MAAM;EAC/C,MAAM;IAAEC,UAAU;IAAEC;EAAW,CAAC,GAAGtB,IAAI;EAEvC,IAEEqB,UAAU,IACV,CAACF,uBAAuB,IACxBnB,IAAI,CAACuB,KAAK,KACTvB,IAAI,CAACuB,KAAK,CAACC,sBAAsB,IAChCxB,IAAI,CAACuB,KAAK,CAACE,0BAA0B,KAGX,CAACV,YAAY,EACzC;IACAA,YAAY,GAAG,IAAI;IACnBW,OAAO,CAACC,IAAI,CAAC;AACjB;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;EACA;EAEA,MAAMC,gBAAgB,GACpBT,uBAAuB,KAAK,QAAQ,IACnC,CAACA,uBAAuB,IAAIG,UAAW;EAE1C,IAAI,CAACpB,IAAI,CAAC0B,gBAAgB,GAAG,QAAQ,GAAG,MAAM,CAAC;EAC/C,IAAI,CAACzB,KAAK,CAAC,CAAC;EAEZ,IAEE,CAACyB,gBAAgB,KAChBT,uBAAuB,KAAK,aAAa,IACvC,CAACA,uBAAuB,KAAAD,WAAA,GAAIlB,IAAI,CAACuB,KAAK,aAAVL,WAAA,CAAYO,0BAA2B,GACtE;IAEA,IAAI,CAACI,SAAS,CAACR,UAAU,IAAIC,UAAU,CAAC;IACxC;EACF;EAEA,MAAMQ,eAAe,GAAGb,gBAAgB,GAAG,CAAC,GAAG,CAAC;EAEhD,IAAI,CAACH,KAAK,CAAC,GAAG,EAAE,IAAI,EAAEgB,eAAe,CAAC;EACtC,IAAI,CAAC3B,KAAK,CAAC,CAAC;EACZ,IAAI,CAAC0B,SAAS,CAACR,UAAU,IAAIC,UAAU,EAAE,IAAI,CAACS,wBAAwB,CAAC,GAAG,CAAC,CAAC;EAC5E,IAAI,CAAC5B,KAAK,CAAC,CAAC;EACZ,IAAI,CAACW,KAAK,CAAC,GAAG,EAAE,IAAI,EAAEgB,eAAe,CAAC;AACxC;AAEO,SAASE,oBAAoBA,CAElChC,IAA4D,EAC5D;EAAA,IAAAiC,gBAAA,EAAAC,gBAAA;EACA,IAAI,CAAChC,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAIH,IAAI,CAACY,UAAU,KAAK,MAAM,EAAE;IAC9B,IAAI,CAACV,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACW,SAAK,GAAI,CAAC;EACf,IAAI,CAACX,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAAA8B,gBAAA,GAAAjC,IAAI,CAACqB,UAAU,aAAfY,gBAAA,CAAiBE,MAAM,KAAAD,gBAAA,GAAIlC,IAAI,CAACsB,UAAU,aAAfY,gBAAA,CAAiBC,MAAM,EAAE;IACtD,IAAI,CAAC/B,KAAK,CAACJ,IAAI,CAACoC,MAAM,EAAE,IAAI,CAAC;IAC7B,IAAI,CAACjC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACa,gBAAgB,CAAChB,IAAI,EAAE,KAAK,CAAC;EACpC,CAAC,MAAM;IACL,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACoC,MAAM,CAAC;EACzB;EAEA,IAAI,CAACC,SAAS,CAAC,CAAC;AAClB;AAEA,SAASC,gCAAgCA,CACvCC,OAAgB,EAChBvC,IAA2D,EAC3D;EACA,IACEP,kBAAkB,CAACO,IAAI,CAACwC,WAAW,CAAC,IACpCD,OAAO,CAACE,kCAAkC,CACxCzC,IACF,CAAC,EACD;IACAuC,OAAO,CAACG,SAAS,CAAC1C,IAAI,CAACwC,WAAW,CAACG,UAAU,CAAC;EAChD;AACF;AAEO,SAASC,sBAAsBA,CAEpC5C,IAA8B,EAC9B;EACAsC,gCAAgC,CAAC,IAAI,EAAEtC,IAAI,CAAC;EAE5C,IAAI,CAACE,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAIH,IAAI,CAACwC,WAAW,EAAE;IACpB,MAAMK,MAAM,GAAG7C,IAAI,CAACwC,WAAW;IAC/B,IAAI,CAACpC,KAAK,CAACyC,MAAM,CAAC;IAClB,IAAI,CAAC/C,WAAW,CAAC+C,MAAM,CAAC,EAAE,IAAI,CAACR,SAAS,CAAC,CAAC;EAC5C,CAAC,MAAM;IACL,IAAIrC,IAAI,CAACY,UAAU,KAAK,MAAM,EAAE;MAC9B,IAAI,CAACV,IAAI,CAAC,MAAM,CAAC;MACjB,IAAI,CAACC,KAAK,CAAC,CAAC;IACd;IAEA,MAAM2C,UAAU,GAAG9C,IAAI,CAAC8C,UAAU,CAACC,KAAK,CAAC,CAAC,CAAC;IAG3C,IAAIC,UAAU,GAAG,KAAK;IACtB,SAAS;MACP,MAAMC,KAAK,GAAGH,UAAU,CAAC,CAAC,CAAC;MAC3B,IACEpD,wBAAwB,CAACuD,KAAK,CAAC,IAC/BtD,0BAA0B,CAACsD,KAAK,CAAC,EACjC;QACAD,UAAU,GAAG,IAAI;QACjB,IAAI,CAAC5C,KAAK,CAAC0C,UAAU,CAACI,KAAK,CAAC,CAAC,CAAC;QAC9B,IAAIJ,UAAU,CAACX,MAAM,EAAE;UACrB,IAAI,CAACrB,SAAK,GAAI,CAAC;UACf,IAAI,CAACX,KAAK,CAAC,CAAC;QACd;MACF,CAAC,MAAM;QACL;MACF;IACF;IAEA,IAAIgD,QAAQ,GAAG,KAAK;IACpB,IAAIL,UAAU,CAACX,MAAM,IAAK,CAACW,UAAU,CAACX,MAAM,IAAI,CAACa,UAAW,EAAE;MAC5DG,QAAQ,GAAG,IAAI;MACf,IAAI,CAACrC,SAAK,IAAI,CAAC;MACf,IAAIgC,UAAU,CAACX,MAAM,EAAE;QACrB,IAAI,CAAChC,KAAK,CAAC,CAAC;QACZ,IAAI,CAAC0B,SAAS,CAACiB,UAAU,EAAE,IAAI,CAACf,wBAAwB,CAAC,GAAG,CAAC,CAAC;QAC9D,IAAI,CAAC5B,KAAK,CAAC,CAAC;MACd;MACA,IAAI,CAACW,SAAK,IAAI,CAAC;IACjB;IAEA,IAAId,IAAI,CAACoC,MAAM,EAAE;MAAA,IAAAgB,iBAAA,EAAAC,iBAAA;MACf,IAAI,CAAClD,KAAK,CAAC,CAAC;MACZ,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;MACjB,IAAI,CAACC,KAAK,CAAC,CAAC;MACZ,IAAI,CAAAiD,iBAAA,GAAApD,IAAI,CAACqB,UAAU,aAAf+B,iBAAA,CAAiBjB,MAAM,KAAAkB,iBAAA,GAAIrD,IAAI,CAACsB,UAAU,aAAf+B,iBAAA,CAAiBlB,MAAM,EAAE;QACtD,IAAI,CAAC/B,KAAK,CAACJ,IAAI,CAACoC,MAAM,EAAE,IAAI,CAAC;QAC7B,IAAI,CAACjC,KAAK,CAAC,CAAC;QACZ,IAAI,CAACa,gBAAgB,CAAChB,IAAI,EAAEmD,QAAQ,CAAC;MACvC,CAAC,MAAM;QACL,IAAI,CAAC/C,KAAK,CAACJ,IAAI,CAACoC,MAAM,CAAC;MACzB;IACF;IAEA,IAAI,CAACC,SAAS,CAAC,CAAC;EAClB;AACF;AAEO,SAASiB,wBAAwBA,CAEtCtD,IAAgC,EAChC;EACAsC,gCAAgC,CAAC,IAAI,EAAEtC,IAAI,CAAC;EAE5C,IAAI,CAACE,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACqD,yBAAyB,CAAC,CAAC;EAChC,IAAI,CAACpD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACqD,YAAY,IAAIC,mBAAY,CAACC,aAAa;EAC/C,MAAMb,MAAM,GAAG7C,IAAI,CAACwC,WAAW;EAC/B,IAAI,CAACpC,KAAK,CAACyC,MAAM,CAAC;EAClB,IAAI,CAAC/C,WAAW,CAAC+C,MAAM,CAAC,EAAE,IAAI,CAACR,SAAS,CAAC,CAAC;AAC5C;AAEO,SAASsB,iBAAiBA,CAAgB3D,IAAyB,EAAE;EAAA,IAAA4D,iBAAA,EAAAC,iBAAA;EAC1E,IAAI,CAAC3D,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EAEZ,MAAM2D,UAAU,GAAG9D,IAAI,CAACC,UAAU,KAAK,MAAM,IAAID,IAAI,CAACC,UAAU,KAAK,QAAQ;EAC7E,IAAI6D,UAAU,EAAE;IACd,IAAI,CAACP,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACrD,IAAI,CAACF,IAAI,CAACC,UAAU,CAAC;IAC1B,IAAI,CAACE,KAAK,CAAC,CAAC;EACd,CAAC,MAAM,IAAIH,IAAI,CAAC+D,MAAM,EAAE;IACtB,IAAI,CAACR,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACrD,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd,CAAC,MAAM,IAAIH,IAAI,CAACgE,KAAK,EAAE;IACrB,IAAI,CAACT,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACrD,IAAI,CAACF,IAAI,CAACgE,KAAK,CAAC;IACrB,IAAI,CAAC7D,KAAK,CAAC,CAAC;EACd;EAEA,MAAM2C,UAAU,GAAG9C,IAAI,CAAC8C,UAAU,CAACC,KAAK,CAAC,CAAC,CAAC;EAC3C,MAAMkB,aAAa,GAAG,CAAC,CAACnB,UAAU,CAACX,MAAM;EAGzC,OAAO8B,aAAa,EAAE;IACpB,MAAMhB,KAAK,GAAGH,UAAU,CAAC,CAAC,CAAC;IAC3B,IAAIlD,wBAAwB,CAACqD,KAAK,CAAC,IAAIpD,0BAA0B,CAACoD,KAAK,CAAC,EAAE;MACxE,IAAI,CAAC7C,KAAK,CAAC0C,UAAU,CAACI,KAAK,CAAC,CAAC,CAAC;MAC9B,IAAIJ,UAAU,CAACX,MAAM,EAAE;QACrB,IAAI,CAACrB,SAAK,GAAI,CAAC;QACf,IAAI,CAACX,KAAK,CAAC,CAAC;MACd;IACF,CAAC,MAAM;MACL;IACF;EACF;EAEA,IAAIgD,QAAQ,GAAG,KAAK;EACpB,IAAIL,UAAU,CAACX,MAAM,EAAE;IACrBgB,QAAQ,GAAG,IAAI;IACf,IAAI,CAACrC,SAAK,IAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAAC0B,SAAS,CAACiB,UAAU,EAAE,IAAI,CAACf,wBAAwB,CAAC,GAAG,CAAC,CAAC;IAC9D,IAAI,CAAC5B,KAAK,CAAC,CAAC;IACZ,IAAI,CAACW,SAAK,IAAI,CAAC;EACjB,CAAC,MAAM,IAAIgD,UAAU,IAAI,CAACG,aAAa,EAAE;IACvCd,QAAQ,GAAG,IAAI;IACf,IAAI,CAACrC,SAAK,IAAI,CAAC;IACf,IAAI,CAACA,SAAK,IAAI,CAAC;EACjB;EAEA,IAAImD,aAAa,IAAIH,UAAU,EAAE;IAC/B,IAAI,CAAC3D,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAAAyD,iBAAA,GAAA5D,IAAI,CAACqB,UAAU,aAAfuC,iBAAA,CAAiBzB,MAAM,KAAA0B,iBAAA,GAAI7D,IAAI,CAACsB,UAAU,aAAfuC,iBAAA,CAAiB1B,MAAM,EAAE;IACtD,IAAI,CAAC/B,KAAK,CAACJ,IAAI,CAACoC,MAAM,EAAE,IAAI,CAAC;IAC7B,IAAI,CAACjC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACa,gBAAgB,CAAChB,IAAI,EAAEmD,QAAQ,CAAC;EACvC,CAAC,MAAM;IACL,IAAI,CAAC/C,KAAK,CAACJ,IAAI,CAACoC,MAAM,CAAC;EACzB;EAEA,IAAI,CAACC,SAAS,CAAC,CAAC;AAClB;AAEO,SAAS6B,eAAeA,CAAgBlE,IAAuB,EAAE;EACtE,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACmE,GAAG,CAAC;EACpB,IAAI,CAACrD,SAAK,GAAI,CAAC;EACf,IAAI,CAACX,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACoE,KAAK,CAAC;AACxB;AAEO,SAASC,wBAAwBA,CAEtCrE,IAAgC,EAChC;EACA,IAAI,CAACc,SAAK,GAAI,CAAC;EACf,IAAI,CAACX,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACM,KAAK,CAAC;AACxB;AAEO,SAASgE,gBAAgBA,CAAgBtE,IAAwB,EAAE;EACxE,IAAI,CAACE,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAIF,IAAI,CAACgE,KAAK,EAAE;IACd,IAAI,CAAClD,SAAK,GAAI,CAAC;IACf,IAAI,CAACZ,IAAI,CAACF,IAAI,CAACgE,KAAK,CAAC;EACvB;EACA,IAAI,CAAClD,SAAK,GAAI,CAAC;EACf,MAAMiB,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAAC,GAAG,CAAC;EACnE,IAAI,CAAC3B,KAAK,CAACJ,IAAI,CAACoC,MAAM,CAAC;EACvB,IAAIpC,IAAI,CAACuE,OAAO,IAAI,IAAI,EAAE;IACxB,IAAI,CAACzD,SAAK,GAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACuE,OAAO,CAAC;EAC1B;EACA,IAAIxC,wBAAwB,EAAE;IAC5B,IAAI,CAACjB,SAAK,GAAI,CAAC;EACjB;EACA,IAAI,CAAC0D,WAAW,CAACxE,IAAI,CAAC;AACxB", "ignoreList": []}